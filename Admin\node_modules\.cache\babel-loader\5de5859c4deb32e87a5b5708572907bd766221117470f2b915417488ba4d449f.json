{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport { useCallback, useRef, useState } from 'react';\nimport * as Utils from './utils';\nfunction useUncontrolledProp(propValue, defaultValue, handler) {\n  var wasPropRef = useRef(propValue !== undefined);\n  var _useState = useState(defaultValue),\n    stateValue = _useState[0],\n    setState = _useState[1];\n  var isProp = propValue !== undefined;\n  var wasProp = wasPropRef.current;\n  wasPropRef.current = isProp;\n  /**\n   * If a prop switches from controlled to Uncontrolled\n   * reset its value to the defaultValue\n   */\n\n  if (!isProp && wasProp && stateValue !== defaultValue) {\n    setState(defaultValue);\n  }\n  return [isProp ? propValue : stateValue, useCallback(function (value) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (handler) handler.apply(void 0, [value].concat(args));\n    setState(value);\n  }, [handler])];\n}\nexport { useUncontrolledProp };\nexport default function useUncontrolled(props, config) {\n  return Object.keys(config).reduce(function (result, fieldName) {\n    var _extends2;\n    var _ref = result,\n      defaultValue = _ref[Utils.defaultKey(fieldName)],\n      propsValue = _ref[fieldName],\n      rest = _objectWithoutPropertiesLoose(_ref, [Utils.defaultKey(fieldName), fieldName].map(_toPropertyKey));\n    var handlerName = config[fieldName];\n    var _useUncontrolledProp = useUncontrolledProp(propsValue, defaultValue, props[handlerName]),\n      value = _useUncontrolledProp[0],\n      handler = _useUncontrolledProp[1];\n    return _extends({}, rest, (_extends2 = {}, _extends2[fieldName] = value, _extends2[handlerName] = handler, _extends2));\n  }, props);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "arg", "key", "_toPrimitive", "String", "input", "hint", "prim", "Symbol", "toPrimitive", "undefined", "res", "call", "TypeError", "Number", "useCallback", "useRef", "useState", "Utils", "useUncontrolledProp", "propValue", "defaultValue", "handler", "wasPropRef", "_useState", "stateValue", "setState", "isProp", "wasProp", "current", "value", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "useUncontrolled", "props", "config", "Object", "keys", "reduce", "result", "fieldName", "_extends2", "_ref", "defaultKey", "props<PERSON><PERSON><PERSON>", "rest", "map", "handler<PERSON>ame", "_useUncontrolledProp"], "sources": ["E:/Uroom/Admin/node_modules/uncontrollable/lib/esm/hook.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\n\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\n\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n\nimport { useCallback, useRef, useState } from 'react';\nimport * as Utils from './utils';\n\nfunction useUncontrolledProp(propValue, defaultValue, handler) {\n  var wasPropRef = useRef(propValue !== undefined);\n\n  var _useState = useState(defaultValue),\n      stateValue = _useState[0],\n      setState = _useState[1];\n\n  var isProp = propValue !== undefined;\n  var wasProp = wasPropRef.current;\n  wasPropRef.current = isProp;\n  /**\n   * If a prop switches from controlled to Uncontrolled\n   * reset its value to the defaultValue\n   */\n\n  if (!isProp && wasProp && stateValue !== defaultValue) {\n    setState(defaultValue);\n  }\n\n  return [isProp ? propValue : stateValue, useCallback(function (value) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    if (handler) handler.apply(void 0, [value].concat(args));\n    setState(value);\n  }, [handler])];\n}\n\nexport { useUncontrolledProp };\nexport default function useUncontrolled(props, config) {\n  return Object.keys(config).reduce(function (result, fieldName) {\n    var _extends2;\n\n    var _ref = result,\n        defaultValue = _ref[Utils.defaultKey(fieldName)],\n        propsValue = _ref[fieldName],\n        rest = _objectWithoutPropertiesLoose(_ref, [Utils.defaultKey(fieldName), fieldName].map(_toPropertyKey));\n\n    var handlerName = config[fieldName];\n\n    var _useUncontrolledProp = useUncontrolledProp(propsValue, defaultValue, props[handlerName]),\n        value = _useUncontrolledProp[0],\n        handler = _useUncontrolledProp[1];\n\n    return _extends({}, rest, (_extends2 = {}, _extends2[fieldName] = value, _extends2[handlerName] = handler, _extends2));\n  }, props);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AAEnG,SAASC,cAAcA,CAACC,GAAG,EAAE;EAAE,IAAIC,GAAG,GAAGC,YAAY,CAACF,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO,OAAOC,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGE,MAAM,CAACF,GAAG,CAAC;AAAE;AAE1H,SAASC,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,WAAW,CAAC;EAAE,IAAIF,IAAI,KAAKG,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACP,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACP,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGU,MAAM,EAAET,KAAK,CAAC;AAAE;AAExX,SAASU,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrD,OAAO,KAAKC,KAAK,MAAM,SAAS;AAEhC,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAAE;EAC7D,IAAIC,UAAU,GAAGP,MAAM,CAACI,SAAS,KAAKV,SAAS,CAAC;EAEhD,IAAIc,SAAS,GAAGP,QAAQ,CAACI,YAAY,CAAC;IAClCI,UAAU,GAAGD,SAAS,CAAC,CAAC,CAAC;IACzBE,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC;EAE3B,IAAIG,MAAM,GAAGP,SAAS,KAAKV,SAAS;EACpC,IAAIkB,OAAO,GAAGL,UAAU,CAACM,OAAO;EAChCN,UAAU,CAACM,OAAO,GAAGF,MAAM;EAC3B;AACF;AACA;AACA;;EAEE,IAAI,CAACA,MAAM,IAAIC,OAAO,IAAIH,UAAU,KAAKJ,YAAY,EAAE;IACrDK,QAAQ,CAACL,YAAY,CAAC;EACxB;EAEA,OAAO,CAACM,MAAM,GAAGP,SAAS,GAAGK,UAAU,EAAEV,WAAW,CAAC,UAAUe,KAAK,EAAE;IACpE,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAClC;IAEA,IAAId,OAAO,EAAEA,OAAO,CAACe,KAAK,CAAC,KAAK,CAAC,EAAE,CAACP,KAAK,CAAC,CAACQ,MAAM,CAACJ,IAAI,CAAC,CAAC;IACxDR,QAAQ,CAACI,KAAK,CAAC;EACjB,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC,CAAC;AAChB;AAEA,SAASH,mBAAmB;AAC5B,eAAe,SAASoB,eAAeA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,MAAM,CAAC,UAAUC,MAAM,EAAEC,SAAS,EAAE;IAC7D,IAAIC,SAAS;IAEb,IAAIC,IAAI,GAAGH,MAAM;MACbxB,YAAY,GAAG2B,IAAI,CAAC9B,KAAK,CAAC+B,UAAU,CAACH,SAAS,CAAC,CAAC;MAChDI,UAAU,GAAGF,IAAI,CAACF,SAAS,CAAC;MAC5BK,IAAI,GAAGpD,6BAA6B,CAACiD,IAAI,EAAE,CAAC9B,KAAK,CAAC+B,UAAU,CAACH,SAAS,CAAC,EAAEA,SAAS,CAAC,CAACM,GAAG,CAACpD,cAAc,CAAC,CAAC;IAE5G,IAAIqD,WAAW,GAAGZ,MAAM,CAACK,SAAS,CAAC;IAEnC,IAAIQ,oBAAoB,GAAGnC,mBAAmB,CAAC+B,UAAU,EAAE7B,YAAY,EAAEmB,KAAK,CAACa,WAAW,CAAC,CAAC;MACxFvB,KAAK,GAAGwB,oBAAoB,CAAC,CAAC,CAAC;MAC/BhC,OAAO,GAAGgC,oBAAoB,CAAC,CAAC,CAAC;IAErC,OAAOxD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,IAAI,GAAGJ,SAAS,GAAG,CAAC,CAAC,EAAEA,SAAS,CAACD,SAAS,CAAC,GAAGhB,KAAK,EAAEiB,SAAS,CAACM,WAAW,CAAC,GAAG/B,OAAO,EAAEyB,SAAS,CAAC,CAAC;EACxH,CAAC,EAAEP,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}