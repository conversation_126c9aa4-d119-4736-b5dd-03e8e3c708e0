{"ast": null, "code": "import * as React from 'react';\nimport FormCheck from './FormCheck';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Switch = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/_jsx(FormCheck, {\n  ...props,\n  ref: ref,\n  type: \"switch\"\n}));\nSwitch.displayName = 'Switch';\nexport default Object.assign(Switch, {\n  Input: FormCheck.Input,\n  Label: FormCheck.Label\n});", "map": {"version": 3, "names": ["React", "FormCheck", "jsx", "_jsx", "Switch", "forwardRef", "props", "ref", "type", "displayName", "Object", "assign", "Input", "Label"], "sources": ["E:/Uroom/Admin/node_modules/react-bootstrap/esm/Switch.js"], "sourcesContent": ["import * as React from 'react';\nimport FormCheck from './FormCheck';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Switch = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/_jsx(FormCheck, {\n  ...props,\n  ref: ref,\n  type: \"switch\"\n}));\nSwitch.displayName = 'Switch';\nexport default Object.assign(Switch, {\n  Input: FormCheck.Input,\n  Label: FormCheck.Label\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,MAAM,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK,aAAaJ,IAAI,CAACF,SAAS,EAAE;EACxF,GAAGK,KAAK;EACRC,GAAG,EAAEA,GAAG;EACRC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;AACHJ,MAAM,CAACK,WAAW,GAAG,QAAQ;AAC7B,eAAeC,MAAM,CAACC,MAAM,CAACP,MAAM,EAAE;EACnCQ,KAAK,EAAEX,SAAS,CAACW,KAAK;EACtBC,KAAK,EAAEZ,SAAS,CAACY;AACnB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}