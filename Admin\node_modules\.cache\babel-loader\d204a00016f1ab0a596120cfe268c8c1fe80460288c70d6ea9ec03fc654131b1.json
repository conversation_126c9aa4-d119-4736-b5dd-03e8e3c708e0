{"ast": null, "code": "import { CANCEL } from '@redux-saga/symbols';\nvar MAX_SIGNED_INT = 2147483647;\nfunction delayP(ms, val) {\n  if (val === void 0) {\n    val = true;\n  }\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/setTimeout#maximum_delay_value\n  if (process.env.NODE_ENV !== 'production' && ms > MAX_SIGNED_INT) {\n    throw new Error('delay only supports a maximum value of ' + MAX_SIGNED_INT + 'ms');\n  }\n  var timeoutId;\n  var promise = new Promise(function (resolve) {\n    timeoutId = setTimeout(resolve, Math.min(MAX_SIGNED_INT, ms), val);\n  });\n  promise[CANCEL] = function () {\n    clearTimeout(timeoutId);\n  };\n  return promise;\n}\nexport default delayP;", "map": {"version": 3, "names": ["CANCEL", "MAX_SIGNED_INT", "delayP", "ms", "val", "process", "env", "NODE_ENV", "Error", "timeoutId", "promise", "Promise", "resolve", "setTimeout", "Math", "min", "clearTimeout"], "sources": ["E:/Uroom/Admin/node_modules/@redux-saga/delay-p/dist/redux-saga-delay-p.esm.js"], "sourcesContent": ["import { CANCEL } from '@redux-saga/symbols';\n\nvar MAX_SIGNED_INT = 2147483647;\nfunction delayP(ms, val) {\n  if (val === void 0) {\n    val = true;\n  }\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/setTimeout#maximum_delay_value\n  if (process.env.NODE_ENV !== 'production' && ms > MAX_SIGNED_INT) {\n    throw new Error('delay only supports a maximum value of ' + MAX_SIGNED_INT + 'ms');\n  }\n\n  var timeoutId;\n  var promise = new Promise(function (resolve) {\n    timeoutId = setTimeout(resolve, Math.min(MAX_SIGNED_INT, ms), val);\n  });\n\n  promise[CANCEL] = function () {\n    clearTimeout(timeoutId);\n  };\n\n  return promise;\n}\n\nexport default delayP;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,qBAAqB;AAE5C,IAAIC,cAAc,GAAG,UAAU;AAC/B,SAASC,MAAMA,CAACC,EAAE,EAAEC,GAAG,EAAE;EACvB,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,IAAI;EACZ;;EAEA;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIJ,EAAE,GAAGF,cAAc,EAAE;IAChE,MAAM,IAAIO,KAAK,CAAC,yCAAyC,GAAGP,cAAc,GAAG,IAAI,CAAC;EACpF;EAEA,IAAIQ,SAAS;EACb,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;IAC3CH,SAAS,GAAGI,UAAU,CAACD,OAAO,EAAEE,IAAI,CAACC,GAAG,CAACd,cAAc,EAAEE,EAAE,CAAC,EAAEC,GAAG,CAAC;EACpE,CAAC,CAAC;EAEFM,OAAO,CAACV,MAAM,CAAC,GAAG,YAAY;IAC5BgB,YAAY,CAACP,SAAS,CAAC;EACzB,CAAC;EAED,OAAOC,OAAO;AAChB;AAEA,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}