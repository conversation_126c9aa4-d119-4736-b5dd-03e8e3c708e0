{"ast": null, "code": "function deferred() {\n  var def = {};\n  def.promise = new Promise(function (resolve, reject) {\n    def.resolve = resolve;\n    def.reject = reject;\n  });\n  return def;\n}\nfunction arrayOfDeferred(length) {\n  var arr = [];\n  for (var i = 0; i < length; i++) {\n    arr.push(deferred());\n  }\n  return arr;\n}\nexport default deferred;\nexport { arrayOfDeferred };", "map": {"version": 3, "names": ["deferred", "def", "promise", "Promise", "resolve", "reject", "arrayOfDeferred", "length", "arr", "i", "push"], "sources": ["E:/Uroom/Admin/node_modules/@redux-saga/deferred/dist/redux-saga-deferred.esm.js"], "sourcesContent": ["function deferred() {\n  var def = {};\n  def.promise = new Promise(function (resolve, reject) {\n    def.resolve = resolve;\n    def.reject = reject;\n  });\n  return def;\n}\nfunction arrayOfDeferred(length) {\n  var arr = [];\n\n  for (var i = 0; i < length; i++) {\n    arr.push(deferred());\n  }\n\n  return arr;\n}\n\nexport default deferred;\nexport { arrayOfDeferred };\n"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAClB,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZA,GAAG,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IACnDJ,GAAG,CAACG,OAAO,GAAGA,OAAO;IACrBH,GAAG,CAACI,MAAM,GAAGA,MAAM;EACrB,CAAC,CAAC;EACF,OAAOJ,GAAG;AACZ;AACA,SAASK,eAAeA,CAACC,MAAM,EAAE;EAC/B,IAAIC,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;IAC/BD,GAAG,CAACE,IAAI,CAACV,QAAQ,CAAC,CAAC,CAAC;EACtB;EAEA,OAAOQ,GAAG;AACZ;AAEA,eAAeR,QAAQ;AACvB,SAASM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}