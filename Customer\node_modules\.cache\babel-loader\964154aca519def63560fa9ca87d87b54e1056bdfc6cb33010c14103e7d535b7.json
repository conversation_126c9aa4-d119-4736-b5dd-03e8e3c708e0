{"ast": null, "code": "var _jsxFileName = \"E:\\\\Uroom\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\components\\\\PromotionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Modal, <PERSON><PERSON>, Card, Badge, Spinner, Form, InputGroup } from \"react-bootstrap\";\nimport { FaTag, FaTimes, FaCheck, FaPlus } from \"react-icons/fa\";\nimport axios from \"axios\";\nimport Utils from \"../../../../utils/Utils\";\nimport getApiUrl from \"../../../../utils/apiConfig\"; // Add this import\nimport \"../../../../css/PromotionModal.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PromotionModal = ({\n  show,\n  onHide,\n  totalPrice,\n  onApplyPromotion,\n  currentPromotionId\n}) => {\n  _s();\n  const [promotions, setPromotions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\n  const [applying, setApplying] = useState(false);\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [checkingCode, setCheckingCode] = useState(false);\n  const API_BASE_URL = getApiUrl(); // Add this line\n\n  useEffect(() => {\n    if (show && totalPrice > 0) {\n      fetchPromotions();\n    }\n  }, [show, totalPrice]);\n  const fetchPromotions = async () => {\n    setLoading(true);\n    try {\n      let promotionList = [];\n      try {\n        console.log(\"Fetching promotions from API...\");\n        // Replace hardcoded URL with environment-based URL\n        const response = await axios.get(`${API_BASE_URL}/api/promotions`);\n        console.log(\"API Response:\", response.data);\n        promotionList = response.data.promotions || response.data.data || response.data || [];\n        console.log(\"Promotion list from API:\", promotionList);\n        if (!Array.isArray(promotionList) || promotionList.length === 0) {\n          console.log(\"API returned empty or invalid data, using mock data\");\n          throw new Error(\"No promotions from API\");\n        }\n      } catch (apiError) {\n        // Mock data remains the same\n        console.log(\"API Error:\", apiError.message, \"- Using mock promotion data\");\n        promotionList = [{\n          _id: \"1\",\n          code: \"SAVE20\",\n          description: \"Save $20 on orders over $100\",\n          discountType: \"fixed\",\n          discountValue: 20,\n          minOrderAmount: 100,\n          maxDiscount: 20,\n          expiryDate: \"2025-12-31\",\n          isActive: true\n        }, {\n          _id: \"2\",\n          code: \"PERCENT10\",\n          description: \"10% off on all bookings\",\n          discountType: \"percentage\",\n          discountValue: 10,\n          minOrderAmount: 50,\n          maxDiscount: 50,\n          expiryDate: \"2025-12-31\",\n          isActive: true\n        }, {\n          _id: \"3\",\n          code: \"NEWUSER50\",\n          description: \"Special discount for new users\",\n          discountType: \"fixed\",\n          discountValue: 50,\n          minOrderAmount: 200,\n          maxDiscount: 50,\n          expiryDate: \"2025-06-30\",\n          isActive: true\n        }, {\n          _id: \"4\",\n          code: \"EXPIRED\",\n          description: \"This promotion has expired\",\n          discountType: \"fixed\",\n          discountValue: 30,\n          minOrderAmount: 80,\n          maxDiscount: 30,\n          expiryDate: \"2024-12-31\",\n          isActive: false\n        }];\n      }\n      console.log(\"Total price for validation:\", totalPrice);\n      console.log(\"Processing\", promotionList.length, \"promotions\");\n      const validatedPromotions = await Promise.all(promotionList.map(async (promo, index) => {\n        console.log(`Validating promotion ${index + 1}:`, promo.code);\n        try {\n          // Replace hardcoded URL with environment-based URL\n          const validateRes = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n            code: promo.code,\n            orderAmount: totalPrice\n          });\n          console.log(`API validation result for ${promo.code}:`, validateRes.data);\n          return {\n            ...promo,\n            isValid: validateRes.data.valid,\n            discount: validateRes.data.discount || 0,\n            message: validateRes.data.message || \"\"\n          };\n        } catch (err) {\n          console.log(`API validation failed for ${promo.code}, using mock validation`);\n\n          // Mock validation logic nếu API không có\n          const now = new Date();\n          const startDate = new Date(promo.startDate);\n          const endDate = new Date(promo.endDate);\n          const isInTimeRange = now >= startDate && now <= endDate;\n          const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\n          const isActive = promo.isActive !== false;\n          const isValid = isInTimeRange && meetsMinOrder && isActive;\n          let discount = 0;\n          let message = \"\";\n          if (isValid) {\n            if (promo.discountType === \"percentage\") {\n              discount = Math.min(totalPrice * promo.discountValue / 100, promo.maxDiscount || Infinity);\n            } else {\n              discount = Math.min(promo.discountValue, promo.maxDiscount || Infinity);\n            }\n            message = `Save ${discount}`;\n          } else {\n            if (!isInTimeRange) {\n              if (now < startDate) message = \"Promotion has not started yet\";else if (now > endDate) message = \"Promotion has expired\";else message = \"Promotion is not available\";\n            } else if (!meetsMinOrder) message = `Minimum order $${promo.minOrderAmount} required`;else if (!isActive) message = \"Promotion is not active\";else message = \"Not applicable\";\n          }\n          console.log(`Mock validation for ${promo.code}:`, {\n            isValid,\n            discount,\n            message\n          });\n          return {\n            ...promo,\n            isValid,\n            discount,\n            message\n          };\n        }\n      }));\n      console.log(\"Final validated promotions:\", validatedPromotions);\n\n      // Chỉ hiển thị promotion có thể dùng được hoặc sắp có thể dùng (chưa bắt đầu)\n      // Ẩn những promotion đã hết hạn, không đủ điều kiện, hoặc không active\n      const displayPromotions = validatedPromotions.filter(promo => {\n        const now = new Date();\n        const startDate = new Date(promo.startDate || promo.expiryDate || '2025-01-01');\n        const endDate = new Date(promo.endDate || promo.expiryDate || '2025-12-31');\n\n        // Chỉ hiển thị nếu: promotion chưa hết hạn và đang active\n        const notExpired = now <= endDate;\n        const isActive = promo.isActive !== false;\n        return notExpired && isActive;\n      });\n      console.log(\"Display promotions:\", displayPromotions.length, \"of\", validatedPromotions.length);\n      console.log(\"Available now:\", displayPromotions.filter(p => p.isValid).length);\n      console.log(\"Starting soon:\", displayPromotions.filter(p => {\n        var _p$message;\n        return !p.isValid && ((_p$message = p.message) === null || _p$message === void 0 ? void 0 : _p$message.includes(\"not started\"));\n      }).length);\n\n      // Sắp xếp promotions: Available trước, starting soon sau, và theo discount giảm dần\n      const sortedPromotions = displayPromotions.sort((a, b) => {\n        // Available promotions lên trước\n        if (a.isValid && !b.isValid) return -1;\n        if (!a.isValid && b.isValid) return 1;\n\n        // Trong cùng loại, sắp xếp theo discount giảm dần\n        return b.discount - a.discount;\n      });\n      setPromotions(sortedPromotions);\n    } catch (error) {\n      console.error(\"Error fetching promotions:\", error);\n      setPromotions([]);\n    }\n    setLoading(false);\n  };\n  const handleApplyPromotion = async promotion => {\n    if (!promotion.isValid) return;\n    setApplying(true);\n    try {\n      try {\n        // Replace hardcoded URL with environment-based URL\n        const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n          code: promotion.code,\n          orderAmount: totalPrice\n        });\n        if (response.data.valid) {\n          onApplyPromotion({\n            code: promotion.code,\n            discount: response.data.discount,\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\n            promotionId: response.data.promotionId\n          });\n          onHide();\n        }\n      } catch (apiError) {\n        // Mock logic remains the same\n        console.log(\"Using mock promotion application\");\n        onApplyPromotion({\n          code: promotion.code,\n          discount: promotion.discount,\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\n          promotionId: promotion._id\n        });\n        onHide();\n      }\n    } catch (error) {\n      console.error(\"Error applying promotion:\", error);\n    }\n    setApplying(false);\n  };\n  const handleRemovePromotion = () => {\n    onApplyPromotion({\n      code: \"\",\n      discount: 0,\n      message: \"\",\n      promotionId: null\n    });\n    onHide();\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\",\n        color: \"white\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaTag, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), \"Select Promotion\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        color: \"white\",\n        maxHeight: \"60vh\",\n        overflowY: \"auto\"\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          variant: \"light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: \"Loading promotions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [currentPromotionId && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Current Applied Promotion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"promotion-card current-promotion\",\n            style: {\n              backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n              borderColor: \"#28a745\",\n              border: \"2px solid #28a745\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"py-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"text-success me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success fw-bold\",\n                    children: \"Applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-danger\",\n                  size: \"sm\",\n                  onClick: handleRemovePromotion,\n                  disabled: applying,\n                  children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 25\n                  }, this), \"Remove\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-3\",\n          children: [\"Available Promotions\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"small ms-2\",\n            style: {\n              color: 'rgba(255,255,255,0.6)'\n            },\n            children: [\"(\", promotions.filter(p => p.isValid).length, \" ready, \", promotions.filter(p => !p.isValid).length, \" starting soon)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this), promotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          style: {\n            color: 'rgba(255,255,255,0.7)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n            size: 48,\n            className: \"mb-3\",\n            style: {\n              opacity: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No promotions available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [promotions.filter(p => p.isValid).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row g-3 mb-4\",\n            children: promotions.filter(p => p.isValid).map(promotion => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: `promotion-card ${currentPromotionId === promotion._id ? 'current' : ''}`,\n                style: {\n                  backgroundColor: currentPromotionId === promotion._id ? \"rgba(40, 167, 69, 0.2)\" : \"rgba(255,255,255,0.1)\",\n                  borderColor: currentPromotionId === promotion._id ? \"#28a745\" : \"rgba(255,255,255,0.3)\",\n                  cursor: \"pointer\",\n                  transition: \"all 0.3s ease\"\n                },\n                onClick: () => handleApplyPromotion(promotion),\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"py-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-start\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-grow-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                          className: \"me-2 text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 343,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0 fw-bold\",\n                          children: promotion.code\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 344,\n                          columnNumber: 35\n                        }, this), currentPromotionId === promotion._id && /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          className: \"ms-2\",\n                          children: \"Applied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 346,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          className: \"ms-2\",\n                          children: \"Available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 348,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 342,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mb-2 small\",\n                        style: {\n                          color: 'rgba(255,255,255,0.7)'\n                        },\n                        children: promotion.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 351,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-between align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-success fw-bold\",\n                            children: [\"Save \", Utils.formatCurrency(promotion.discount)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 355,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 354,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-end\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"small\",\n                            children: [promotion.minOrderAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-success\",\n                              children: [\"Min: \", Utils.formatCurrency(promotion.minOrderAmount), \" \\u2713\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 363,\n                              columnNumber: 41\n                            }, this), promotion.maxDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                color: 'rgba(255,255,255,0.6)'\n                              },\n                              children: [\"Max: \", Utils.formatCurrency(promotion.maxDiscount)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 368,\n                              columnNumber: 41\n                            }, this), promotion.expiryDate && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-success\",\n                              children: [\"Expires: \", new Date(promotion.expiryDate).toLocaleDateString(), \" \\u2713\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 373,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 361,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 360,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 25\n              }, this)\n            }, promotion._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 19\n          }, this), promotions.filter(p => !p.isValid).length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-3 text-warning\",\n              children: [\"Starting Soon (\", promotions.filter(p => !p.isValid).length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row g-3\",\n              children: promotions.filter(p => !p.isValid).map(promotion => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12\",\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-card disabled\",\n                  style: {\n                    backgroundColor: \"rgba(255, 193, 7, 0.1)\",\n                    borderColor: \"rgba(255, 193, 7, 0.5)\",\n                    cursor: \"not-allowed\",\n                    opacity: 0.8,\n                    transition: \"all 0.3s ease\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-start\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-grow-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"me-2 text-warning\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 412,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"mb-0 fw-bold\",\n                            children: promotion.code\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 413,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"warning\",\n                            className: \"ms-2\",\n                            style: {\n                              color: 'white'\n                            },\n                            children: \"Starting Soon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 414,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 411,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"mb-2 small\",\n                          style: {\n                            color: 'rgba(255,255,255,0.7)'\n                          },\n                          children: promotion.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 417,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex justify-content-between align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-warning small fw-bold\",\n                              children: promotion.message\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 421,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 420,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-end\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"small\",\n                              children: [promotion.minOrderAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `${totalPrice >= promotion.minOrderAmount ? 'text-success' : 'text-warning'}`,\n                                children: [\"Min: \", Utils.formatCurrency(promotion.minOrderAmount), totalPrice >= promotion.minOrderAmount ? ' ✓' : ' ✗']\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 429,\n                                columnNumber: 43\n                              }, this), promotion.maxDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n                                style: {\n                                  color: 'rgba(255,255,255,0.6)'\n                                },\n                                children: [\"Max: \", Utils.formatCurrency(promotion.maxDiscount)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 435,\n                                columnNumber: 43\n                              }, this), (promotion.startDate || promotion.expiryDate) && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-warning\",\n                                children: [\"Starts: \", new Date(promotion.startDate || promotion.expiryDate).toLocaleDateString()]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 440,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 427,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 426,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 419,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 410,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 27\n                }, this)\n              }, promotion._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-light\",\n        onClick: onHide,\n        disabled: applying,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(PromotionModal, \"4aWHLDgIuAZPWkATBXonslzcXws=\");\n_c = PromotionModal;\nexport default PromotionModal;\nvar _c;\n$RefreshReg$(_c, \"PromotionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "<PERSON><PERSON>", "Card", "Badge", "Spinner", "Form", "InputGroup", "FaTag", "FaTimes", "FaCheck", "FaPlus", "axios", "Utils", "getApiUrl", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PromotionModal", "show", "onHide", "totalPrice", "onApplyPromotion", "currentPromotionId", "_s", "promotions", "setPromotions", "loading", "setLoading", "selectedPromotion", "setSelectedPromotion", "applying", "setApplying", "promotionCode", "setPromotionCode", "checkingCode", "setCheckingCode", "API_BASE_URL", "fetchPromotions", "promotionList", "console", "log", "response", "get", "data", "Array", "isArray", "length", "Error", "apiError", "message", "_id", "code", "description", "discountType", "discountValue", "minOrderAmount", "maxDiscount", "expiryDate", "isActive", "validatedPromotions", "Promise", "all", "map", "promo", "index", "validateRes", "post", "orderAmount", "<PERSON><PERSON><PERSON><PERSON>", "valid", "discount", "err", "now", "Date", "startDate", "endDate", "isInTimeRange", "meetsMinOrder", "Math", "min", "Infinity", "displayPromotions", "filter", "notExpired", "p", "_p$message", "includes", "sortedPromotions", "sort", "a", "b", "error", "handleApplyPromotion", "promotion", "formatCurrency", "promotionId", "handleRemovePromotion", "size", "centered", "children", "Header", "closeButton", "style", "backgroundColor", "borderColor", "color", "Title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "maxHeight", "overflowY", "animation", "variant", "border", "onClick", "disabled", "opacity", "cursor", "transition", "bg", "toLocaleDateString", "Footer", "_c", "$RefreshReg$"], "sources": ["E:/Uroom/Customer/src/pages/customer/home/<USER>/PromotionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ge, Spinner, Form, InputGroup } from \"react-bootstrap\";\r\nimport { FaTag, FaTimes, FaCheck, FaPlus } from \"react-icons/fa\";\r\nimport axios from \"axios\";\r\nimport Utils from \"../../../../utils/Utils\";\r\nimport getApiUrl from \"../../../../utils/apiConfig\"; // Add this import\r\nimport \"../../../../css/PromotionModal.css\";\r\n\r\nconst PromotionModal = ({ show, onHide, totalPrice, onApplyPromotion, currentPromotionId }) => {\r\n  const [promotions, setPromotions] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\r\n  const [applying, setApplying] = useState(false);\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [checkingCode, setCheckingCode] = useState(false);\r\n\r\n  const API_BASE_URL = getApiUrl(); // Add this line\r\n\r\n  useEffect(() => {\r\n    if (show && totalPrice > 0) {\r\n      fetchPromotions();\r\n    }\r\n  }, [show, totalPrice]);\r\n\r\n  const fetchPromotions = async () => {\r\n    setLoading(true);\r\n    try {\r\n      let promotionList = [];\r\n      try {\r\n        console.log(\"Fetching promotions from API...\");\r\n        // Replace hardcoded URL with environment-based URL\r\n        const response = await axios.get(`${API_BASE_URL}/api/promotions`);\r\n        console.log(\"API Response:\", response.data);\r\n        \r\n        promotionList = response.data.promotions || response.data.data || response.data || [];\r\n        console.log(\"Promotion list from API:\", promotionList);\r\n        \r\n        if (!Array.isArray(promotionList) || promotionList.length === 0) {\r\n          console.log(\"API returned empty or invalid data, using mock data\");\r\n          throw new Error(\"No promotions from API\");\r\n        }\r\n      } catch (apiError) {\r\n        // Mock data remains the same\r\n        console.log(\"API Error:\", apiError.message, \"- Using mock promotion data\");\r\n        promotionList = [\r\n          {\r\n            _id: \"1\",\r\n            code: \"SAVE20\",\r\n            description: \"Save $20 on orders over $100\",\r\n            discountType: \"fixed\",\r\n            discountValue: 20,\r\n            minOrderAmount: 100,\r\n            maxDiscount: 20,\r\n            expiryDate: \"2025-12-31\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"2\", \r\n            code: \"PERCENT10\",\r\n            description: \"10% off on all bookings\",\r\n            discountType: \"percentage\",\r\n            discountValue: 10,\r\n            minOrderAmount: 50,\r\n            maxDiscount: 50,\r\n            expiryDate: \"2025-12-31\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"3\",\r\n            code: \"NEWUSER50\",\r\n            description: \"Special discount for new users\",\r\n            discountType: \"fixed\", \r\n            discountValue: 50,\r\n            minOrderAmount: 200,\r\n            maxDiscount: 50,\r\n            expiryDate: \"2025-06-30\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"4\",\r\n            code: \"EXPIRED\",\r\n            description: \"This promotion has expired\",\r\n            discountType: \"fixed\",\r\n            discountValue: 30,\r\n            minOrderAmount: 80,\r\n            maxDiscount: 30,\r\n            expiryDate: \"2024-12-31\",\r\n            isActive: false\r\n          }\r\n        ];\r\n      }\r\n      \r\n      console.log(\"Total price for validation:\", totalPrice);\r\n      console.log(\"Processing\", promotionList.length, \"promotions\");\r\n      \r\n      const validatedPromotions = await Promise.all(\r\n        promotionList.map(async (promo, index) => {\r\n          console.log(`Validating promotion ${index + 1}:`, promo.code);\r\n          \r\n          try {\r\n            // Replace hardcoded URL with environment-based URL\r\n            const validateRes = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n              code: promo.code,\r\n              orderAmount: totalPrice,\r\n            });\r\n            console.log(`API validation result for ${promo.code}:`, validateRes.data);\r\n            \r\n            return {\r\n              ...promo,\r\n              isValid: validateRes.data.valid,\r\n              discount: validateRes.data.discount || 0,\r\n              message: validateRes.data.message || \"\",\r\n            };\r\n          } catch (err) {\r\n            console.log(`API validation failed for ${promo.code}, using mock validation`);\r\n            \r\n            // Mock validation logic nếu API không có\r\n            const now = new Date();\r\n            const startDate = new Date(promo.startDate);\r\n            const endDate = new Date(promo.endDate);\r\n            \r\n            const isInTimeRange = now >= startDate && now <= endDate;\r\n            const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\r\n            const isActive = promo.isActive !== false;\r\n            \r\n            const isValid = isInTimeRange && meetsMinOrder && isActive;\r\n            \r\n            let discount = 0;\r\n            let message = \"\";\r\n            \r\n            if (isValid) {\r\n              if (promo.discountType === \"percentage\") {\r\n                discount = Math.min((totalPrice * promo.discountValue) / 100, promo.maxDiscount || Infinity);\r\n              } else {\r\n                discount = Math.min(promo.discountValue, promo.maxDiscount || Infinity);\r\n              }\r\n              message = `Save ${discount}`;\r\n            } else {\r\n              if (!isInTimeRange) {\r\n                if (now < startDate) message = \"Promotion has not started yet\";\r\n                else if (now > endDate) message = \"Promotion has expired\";\r\n                else message = \"Promotion is not available\";\r\n              } else if (!meetsMinOrder) message = `Minimum order $${promo.minOrderAmount} required`;\r\n              else if (!isActive) message = \"Promotion is not active\";\r\n              else message = \"Not applicable\";\r\n            }\r\n            \r\n            console.log(`Mock validation for ${promo.code}:`, { isValid, discount, message });\r\n            \r\n            return {\r\n              ...promo,\r\n              isValid,\r\n              discount,\r\n              message,\r\n            };\r\n          }\r\n        })\r\n      );\r\n      \r\n      console.log(\"Final validated promotions:\", validatedPromotions);\r\n      \r\n      // Chỉ hiển thị promotion có thể dùng được hoặc sắp có thể dùng (chưa bắt đầu)\r\n      // Ẩn những promotion đã hết hạn, không đủ điều kiện, hoặc không active\r\n      const displayPromotions = validatedPromotions.filter(promo => {\r\n        const now = new Date();\r\n        const startDate = new Date(promo.startDate || promo.expiryDate || '2025-01-01');\r\n        const endDate = new Date(promo.endDate || promo.expiryDate || '2025-12-31');\r\n        \r\n        // Chỉ hiển thị nếu: promotion chưa hết hạn và đang active\r\n        const notExpired = now <= endDate;\r\n        const isActive = promo.isActive !== false;\r\n        \r\n        return notExpired && isActive;\r\n      });\r\n      \r\n      console.log(\"Display promotions:\", displayPromotions.length, \"of\", validatedPromotions.length);\r\n      console.log(\"Available now:\", displayPromotions.filter(p => p.isValid).length);\r\n      console.log(\"Starting soon:\", displayPromotions.filter(p => !p.isValid && p.message?.includes(\"not started\")).length);\r\n      \r\n      // Sắp xếp promotions: Available trước, starting soon sau, và theo discount giảm dần\r\n      const sortedPromotions = displayPromotions.sort((a, b) => {\r\n        // Available promotions lên trước\r\n        if (a.isValid && !b.isValid) return -1;\r\n        if (!a.isValid && b.isValid) return 1;\r\n        \r\n        // Trong cùng loại, sắp xếp theo discount giảm dần\r\n        return b.discount - a.discount;\r\n      });\r\n      \r\n      setPromotions(sortedPromotions);\r\n    } catch (error) {\r\n      console.error(\"Error fetching promotions:\", error);\r\n      setPromotions([]);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const handleApplyPromotion = async (promotion) => {\r\n    if (!promotion.isValid) return;\r\n    \r\n    setApplying(true);\r\n    try {\r\n      try {\r\n        // Replace hardcoded URL with environment-based URL\r\n        const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n          code: promotion.code,\r\n          orderAmount: totalPrice,\r\n        });\r\n        \r\n        if (response.data.valid) {\r\n          onApplyPromotion({\r\n            code: promotion.code,\r\n            discount: response.data.discount,\r\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\r\n            promotionId: response.data.promotionId,\r\n          });\r\n          onHide();\r\n        }\r\n      } catch (apiError) {\r\n        // Mock logic remains the same\r\n        console.log(\"Using mock promotion application\");\r\n        onApplyPromotion({\r\n          code: promotion.code,\r\n          discount: promotion.discount,\r\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\r\n          promotionId: promotion._id,\r\n        });\r\n        onHide();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error applying promotion:\", error);\r\n    }\r\n    setApplying(false);\r\n  };\r\n\r\n  const handleRemovePromotion = () => {\r\n    onApplyPromotion({\r\n      code: \"\",\r\n      discount: 0,\r\n      message: \"\",\r\n      promotionId: null,\r\n    });\r\n    onHide();\r\n  };\r\n\r\n  return (\r\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\r\n      <Modal.Header \r\n        closeButton \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\",\r\n          color: \"white\"\r\n        }}\r\n      >\r\n        <Modal.Title className=\"d-flex align-items-center\">\r\n          <FaTag className=\"me-2\" />\r\n          Select Promotion\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      \r\n      <Modal.Body \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          color: \"white\",\r\n          maxHeight: \"60vh\",\r\n          overflowY: \"auto\"\r\n        }}\r\n      >\r\n        {loading ? (\r\n          <div className=\"text-center py-4\">\r\n            <Spinner animation=\"border\" variant=\"light\" />\r\n            <div className=\"mt-2\">Loading promotions...</div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Current promotion section */}\r\n            {currentPromotionId && (\r\n              <div className=\"mb-4\">\r\n                <h6 className=\"mb-3\">Current Applied Promotion</h6>\r\n                <Card \r\n                  className=\"promotion-card current-promotion\"\r\n                  style={{ \r\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                    borderColor: \"#28a745\",\r\n                    border: \"2px solid #28a745\"\r\n                  }}\r\n                >\r\n                  <Card.Body className=\"py-3\">\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <FaCheck className=\"text-success me-2\" />\r\n                        <span className=\"text-success fw-bold\">Applied</span>\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={handleRemovePromotion}\r\n                        disabled={applying}\r\n                      >\r\n                        <FaTimes className=\"me-1\" />\r\n                        Remove\r\n                      </Button>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n            )}\r\n\r\n            {/* Promotions section */}\r\n            <h6 className=\"mb-3\">\r\n              Available Promotions \r\n              <span className=\"small ms-2\" style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                ({promotions.filter(p => p.isValid).length} ready, {promotions.filter(p => !p.isValid).length} starting soon)\r\n              </span>\r\n            </h6>\r\n            {promotions.length === 0 ? (\r\n              <div className=\"text-center py-4\" style={{color: 'rgba(255,255,255,0.7)'}}>\r\n                <FaTag size={48} className=\"mb-3\" style={{opacity: 0.5}} />\r\n                <div>No promotions available</div>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {/* Available promotions */}\r\n                {promotions.filter(p => p.isValid).length > 0 && (\r\n                  <div className=\"row g-3 mb-4\">\r\n                    {promotions.filter(p => p.isValid).map((promotion) => (\r\n                      <div key={promotion._id} className=\"col-12\">\r\n                        <Card \r\n                          className={`promotion-card ${currentPromotionId === promotion._id ? 'current' : ''}`}\r\n                          style={{ \r\n                            backgroundColor: currentPromotionId === promotion._id ? \"rgba(40, 167, 69, 0.2)\" : \"rgba(255,255,255,0.1)\",\r\n                            borderColor: currentPromotionId === promotion._id ? \"#28a745\" : \"rgba(255,255,255,0.3)\",\r\n                            cursor: \"pointer\",\r\n                            transition: \"all 0.3s ease\"\r\n                          }}\r\n                          onClick={() => handleApplyPromotion(promotion)}\r\n                        >\r\n                          <Card.Body className=\"py-3\">\r\n                            <div className=\"d-flex justify-content-between align-items-start\">\r\n                              <div className=\"flex-grow-1\">\r\n                                <div className=\"d-flex align-items-center mb-2\">\r\n                                  <FaTag className=\"me-2 text-primary\" />\r\n                                  <h6 className=\"mb-0 fw-bold\">{promotion.code}</h6>\r\n                                  {currentPromotionId === promotion._id && (\r\n                                    <Badge bg=\"success\" className=\"ms-2\">Applied</Badge>\r\n                                  )}\r\n                                  <Badge bg=\"success\" className=\"ms-2\">Available</Badge>\r\n                                </div>\r\n                                \r\n                                <p className=\"mb-2 small\" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>\r\n                                \r\n                                <div className=\"d-flex justify-content-between align-items-center\">\r\n                                  <div>\r\n                                    <span className=\"text-success fw-bold\">\r\n                                      Save {Utils.formatCurrency(promotion.discount)}\r\n                                    </span>\r\n                                  </div>\r\n                                  \r\n                                  <div className=\"text-end\">\r\n                                    <div className=\"small\">\r\n                                      {promotion.minOrderAmount && (\r\n                                        <div className=\"text-success\">\r\n                                          Min: {Utils.formatCurrency(promotion.minOrderAmount)} ✓\r\n                                        </div>\r\n                                      )}\r\n                                      {promotion.maxDiscount && (\r\n                                        <div style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                                          Max: {Utils.formatCurrency(promotion.maxDiscount)}\r\n                                        </div>\r\n                                      )}\r\n                                      {promotion.expiryDate && (\r\n                                        <div className=\"text-success\">\r\n                                          Expires: {new Date(promotion.expiryDate).toLocaleDateString()} ✓\r\n                                        </div>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Starting soon promotions */}\r\n                {promotions.filter(p => !p.isValid).length > 0 && (\r\n                  <>\r\n                    <h6 className=\"mb-3 text-warning\">\r\n                      Starting Soon ({promotions.filter(p => !p.isValid).length})\r\n                    </h6>\r\n                    <div className=\"row g-3\">\r\n                      {promotions.filter(p => !p.isValid).map((promotion) => (\r\n                        <div key={promotion._id} className=\"col-12\">\r\n                          <Card \r\n                            className=\"promotion-card disabled\"\r\n                            style={{ \r\n                              backgroundColor: \"rgba(255, 193, 7, 0.1)\",\r\n                              borderColor: \"rgba(255, 193, 7, 0.5)\",\r\n                              cursor: \"not-allowed\",\r\n                              opacity: 0.8,\r\n                              transition: \"all 0.3s ease\"\r\n                            }}\r\n                          >\r\n                            <Card.Body className=\"py-3\">\r\n                              <div className=\"d-flex justify-content-between align-items-start\">\r\n                                <div className=\"flex-grow-1\">\r\n                                  <div className=\"d-flex align-items-center mb-2\">\r\n                                    <FaTag className=\"me-2 text-warning\" />\r\n                                    <h6 className=\"mb-0 fw-bold\">{promotion.code}</h6>\r\n                                    <Badge bg=\"warning\" className=\"ms-2\" style={{color: 'white'}}>Starting Soon</Badge>\r\n                                  </div>\r\n                                  \r\n                                  <p className=\"mb-2 small\" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>\r\n                                  \r\n                                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                                    <div>\r\n                                      <span className=\"text-warning small fw-bold\">\r\n                                        {promotion.message}\r\n                                      </span>\r\n                                    </div>\r\n                                    \r\n                                    <div className=\"text-end\">\r\n                                      <div className=\"small\">\r\n                                        {promotion.minOrderAmount && (\r\n                                          <div className={`${totalPrice >= promotion.minOrderAmount ? 'text-success' : 'text-warning'}`}>\r\n                                            Min: {Utils.formatCurrency(promotion.minOrderAmount)}\r\n                                            {totalPrice >= promotion.minOrderAmount ? ' ✓' : ' ✗'}\r\n                                          </div>\r\n                                        )}\r\n                                        {promotion.maxDiscount && (\r\n                                          <div style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                                            Max: {Utils.formatCurrency(promotion.maxDiscount)}\r\n                                          </div>\r\n                                        )}\r\n                                        {(promotion.startDate || promotion.expiryDate) && (\r\n                                          <div className=\"text-warning\">\r\n                                            Starts: {new Date(promotion.startDate || promotion.expiryDate).toLocaleDateString()}\r\n                                          </div>\r\n                                        )}\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </Card.Body>\r\n                          </Card>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </>\r\n            )}\r\n          </>\r\n        )}\r\n      </Modal.Body>\r\n      \r\n      <Modal.Footer \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\"\r\n        }}\r\n      >\r\n        <Button variant=\"outline-light\" onClick={onHide} disabled={applying}>\r\n          Close\r\n        </Button>\r\n      </Modal.Footer>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default PromotionModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,UAAU,QAAQ,iBAAiB;AACvF,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,SAAS,MAAM,6BAA6B,CAAC,CAAC;AACrD,OAAO,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,UAAU;EAAEC,gBAAgB;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMuC,YAAY,GAAGxB,SAAS,CAAC,CAAC,CAAC,CAAC;;EAElCd,SAAS,CAAC,MAAM;IACd,IAAIoB,IAAI,IAAIE,UAAU,GAAG,CAAC,EAAE;MAC1BiB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACnB,IAAI,EAAEE,UAAU,CAAC,CAAC;EAEtB,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIW,aAAa,GAAG,EAAE;MACtB,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACA,MAAMC,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAAC,GAAGN,YAAY,iBAAiB,CAAC;QAClEG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,QAAQ,CAACE,IAAI,CAAC;QAE3CL,aAAa,GAAGG,QAAQ,CAACE,IAAI,CAACnB,UAAU,IAAIiB,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,IAAI,EAAE;QACrFJ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,aAAa,CAAC;QAEtD,IAAI,CAACM,KAAK,CAACC,OAAO,CAACP,aAAa,CAAC,IAAIA,aAAa,CAACQ,MAAM,KAAK,CAAC,EAAE;UAC/DP,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE,MAAM,IAAIO,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOC,QAAQ,EAAE;QACjB;QACAT,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEQ,QAAQ,CAACC,OAAO,EAAE,6BAA6B,CAAC;QAC1EX,aAAa,GAAG,CACd;UACEY,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,8BAA8B;UAC3CC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,yBAAyB;UACtCC,YAAY,EAAE,YAAY;UAC1BC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,EAAE;UAClBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,gCAAgC;UAC7CC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,SAAS;UACfC,WAAW,EAAE,4BAA4B;UACzCC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,EAAE;UAClBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,CACF;MACH;MAEAnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEpB,UAAU,CAAC;MACtDmB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,aAAa,CAACQ,MAAM,EAAE,YAAY,CAAC;MAE7D,MAAMa,mBAAmB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC3CvB,aAAa,CAACwB,GAAG,CAAC,OAAOC,KAAK,EAAEC,KAAK,KAAK;QACxCzB,OAAO,CAACC,GAAG,CAAC,wBAAwBwB,KAAK,GAAG,CAAC,GAAG,EAAED,KAAK,CAACZ,IAAI,CAAC;QAE7D,IAAI;UACF;UACA,MAAMc,WAAW,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAAC,GAAG9B,YAAY,uBAAuB,EAAE;YAC3Ee,IAAI,EAAEY,KAAK,CAACZ,IAAI;YAChBgB,WAAW,EAAE/C;UACf,CAAC,CAAC;UACFmB,OAAO,CAACC,GAAG,CAAC,6BAA6BuB,KAAK,CAACZ,IAAI,GAAG,EAAEc,WAAW,CAACtB,IAAI,CAAC;UAEzE,OAAO;YACL,GAAGoB,KAAK;YACRK,OAAO,EAAEH,WAAW,CAACtB,IAAI,CAAC0B,KAAK;YAC/BC,QAAQ,EAAEL,WAAW,CAACtB,IAAI,CAAC2B,QAAQ,IAAI,CAAC;YACxCrB,OAAO,EAAEgB,WAAW,CAACtB,IAAI,CAACM,OAAO,IAAI;UACvC,CAAC;QACH,CAAC,CAAC,OAAOsB,GAAG,EAAE;UACZhC,OAAO,CAACC,GAAG,CAAC,6BAA6BuB,KAAK,CAACZ,IAAI,yBAAyB,CAAC;;UAE7E;UACA,MAAMqB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;UACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACV,KAAK,CAACW,SAAS,CAAC;UAC3C,MAAMC,OAAO,GAAG,IAAIF,IAAI,CAACV,KAAK,CAACY,OAAO,CAAC;UAEvC,MAAMC,aAAa,GAAGJ,GAAG,IAAIE,SAAS,IAAIF,GAAG,IAAIG,OAAO;UACxD,MAAME,aAAa,GAAGzD,UAAU,KAAK2C,KAAK,CAACR,cAAc,IAAI,CAAC,CAAC;UAC/D,MAAMG,QAAQ,GAAGK,KAAK,CAACL,QAAQ,KAAK,KAAK;UAEzC,MAAMU,OAAO,GAAGQ,aAAa,IAAIC,aAAa,IAAInB,QAAQ;UAE1D,IAAIY,QAAQ,GAAG,CAAC;UAChB,IAAIrB,OAAO,GAAG,EAAE;UAEhB,IAAImB,OAAO,EAAE;YACX,IAAIL,KAAK,CAACV,YAAY,KAAK,YAAY,EAAE;cACvCiB,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAAE3D,UAAU,GAAG2C,KAAK,CAACT,aAAa,GAAI,GAAG,EAAES,KAAK,CAACP,WAAW,IAAIwB,QAAQ,CAAC;YAC9F,CAAC,MAAM;cACLV,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAAChB,KAAK,CAACT,aAAa,EAAES,KAAK,CAACP,WAAW,IAAIwB,QAAQ,CAAC;YACzE;YACA/B,OAAO,GAAG,QAAQqB,QAAQ,EAAE;UAC9B,CAAC,MAAM;YACL,IAAI,CAACM,aAAa,EAAE;cAClB,IAAIJ,GAAG,GAAGE,SAAS,EAAEzB,OAAO,GAAG,+BAA+B,CAAC,KAC1D,IAAIuB,GAAG,GAAGG,OAAO,EAAE1B,OAAO,GAAG,uBAAuB,CAAC,KACrDA,OAAO,GAAG,4BAA4B;YAC7C,CAAC,MAAM,IAAI,CAAC4B,aAAa,EAAE5B,OAAO,GAAG,kBAAkBc,KAAK,CAACR,cAAc,WAAW,CAAC,KAClF,IAAI,CAACG,QAAQ,EAAET,OAAO,GAAG,yBAAyB,CAAC,KACnDA,OAAO,GAAG,gBAAgB;UACjC;UAEAV,OAAO,CAACC,GAAG,CAAC,uBAAuBuB,KAAK,CAACZ,IAAI,GAAG,EAAE;YAAEiB,OAAO;YAAEE,QAAQ;YAAErB;UAAQ,CAAC,CAAC;UAEjF,OAAO;YACL,GAAGc,KAAK;YACRK,OAAO;YACPE,QAAQ;YACRrB;UACF,CAAC;QACH;MACF,CAAC,CACH,CAAC;MAEDV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmB,mBAAmB,CAAC;;MAE/D;MACA;MACA,MAAMsB,iBAAiB,GAAGtB,mBAAmB,CAACuB,MAAM,CAACnB,KAAK,IAAI;QAC5D,MAAMS,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACV,KAAK,CAACW,SAAS,IAAIX,KAAK,CAACN,UAAU,IAAI,YAAY,CAAC;QAC/E,MAAMkB,OAAO,GAAG,IAAIF,IAAI,CAACV,KAAK,CAACY,OAAO,IAAIZ,KAAK,CAACN,UAAU,IAAI,YAAY,CAAC;;QAE3E;QACA,MAAM0B,UAAU,GAAGX,GAAG,IAAIG,OAAO;QACjC,MAAMjB,QAAQ,GAAGK,KAAK,CAACL,QAAQ,KAAK,KAAK;QAEzC,OAAOyB,UAAU,IAAIzB,QAAQ;MAC/B,CAAC,CAAC;MAEFnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyC,iBAAiB,CAACnC,MAAM,EAAE,IAAI,EAAEa,mBAAmB,CAACb,MAAM,CAAC;MAC9FP,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyC,iBAAiB,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAChB,OAAO,CAAC,CAACtB,MAAM,CAAC;MAC9EP,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyC,iBAAiB,CAACC,MAAM,CAACE,CAAC;QAAA,IAAAC,UAAA;QAAA,OAAI,CAACD,CAAC,CAAChB,OAAO,MAAAiB,UAAA,GAAID,CAAC,CAACnC,OAAO,cAAAoC,UAAA,uBAATA,UAAA,CAAWC,QAAQ,CAAC,aAAa,CAAC;MAAA,EAAC,CAACxC,MAAM,CAAC;;MAErH;MACA,MAAMyC,gBAAgB,GAAGN,iBAAiB,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACxD;QACA,IAAID,CAAC,CAACrB,OAAO,IAAI,CAACsB,CAAC,CAACtB,OAAO,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAACqB,CAAC,CAACrB,OAAO,IAAIsB,CAAC,CAACtB,OAAO,EAAE,OAAO,CAAC;;QAErC;QACA,OAAOsB,CAAC,CAACpB,QAAQ,GAAGmB,CAAC,CAACnB,QAAQ;MAChC,CAAC,CAAC;MAEF7C,aAAa,CAAC8D,gBAAgB,CAAC;IACjC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDlE,aAAa,CAAC,EAAE,CAAC;IACnB;IACAE,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMiE,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,CAACzB,OAAO,EAAE;IAExBrC,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,IAAI;QACF;QACA,MAAMU,QAAQ,GAAG,MAAM/B,KAAK,CAACwD,IAAI,CAAC,GAAG9B,YAAY,uBAAuB,EAAE;UACxEe,IAAI,EAAE0C,SAAS,CAAC1C,IAAI;UACpBgB,WAAW,EAAE/C;QACf,CAAC,CAAC;QAEF,IAAIqB,QAAQ,CAACE,IAAI,CAAC0B,KAAK,EAAE;UACvBhD,gBAAgB,CAAC;YACf8B,IAAI,EAAE0C,SAAS,CAAC1C,IAAI;YACpBmB,QAAQ,EAAE7B,QAAQ,CAACE,IAAI,CAAC2B,QAAQ;YAChCrB,OAAO,EAAE,uBAAuBtC,KAAK,CAACmF,cAAc,CAACrD,QAAQ,CAACE,IAAI,CAAC2B,QAAQ,CAAC,EAAE;YAC9EyB,WAAW,EAAEtD,QAAQ,CAACE,IAAI,CAACoD;UAC7B,CAAC,CAAC;UACF5E,MAAM,CAAC,CAAC;QACV;MACF,CAAC,CAAC,OAAO6B,QAAQ,EAAE;QACjB;QACAT,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CnB,gBAAgB,CAAC;UACf8B,IAAI,EAAE0C,SAAS,CAAC1C,IAAI;UACpBmB,QAAQ,EAAEuB,SAAS,CAACvB,QAAQ;UAC5BrB,OAAO,EAAE,uBAAuBtC,KAAK,CAACmF,cAAc,CAACD,SAAS,CAACvB,QAAQ,CAAC,EAAE;UAC1EyB,WAAW,EAAEF,SAAS,CAAC3C;QACzB,CAAC,CAAC;QACF/B,MAAM,CAAC,CAAC;MACV;IACF,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;IACA5D,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMiE,qBAAqB,GAAGA,CAAA,KAAM;IAClC3E,gBAAgB,CAAC;MACf8B,IAAI,EAAE,EAAE;MACRmB,QAAQ,EAAE,CAAC;MACXrB,OAAO,EAAE,EAAE;MACX8C,WAAW,EAAE;IACf,CAAC,CAAC;IACF5E,MAAM,CAAC,CAAC;EACV,CAAC;EAED,oBACEL,OAAA,CAACf,KAAK;IAACmB,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC8E,IAAI,EAAC,IAAI;IAACC,QAAQ;IAAAC,QAAA,gBACnDrF,OAAA,CAACf,KAAK,CAACqG,MAAM;MACXC,WAAW;MACXC,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE,uBAAuB;QACpCC,KAAK,EAAE;MACT,CAAE;MAAAN,QAAA,eAEFrF,OAAA,CAACf,KAAK,CAAC2G,KAAK;QAACC,SAAS,EAAC,2BAA2B;QAAAR,QAAA,gBAChDrF,OAAA,CAACR,KAAK;UAACqG,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE5B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEfjG,OAAA,CAACf,KAAK,CAACiH,IAAI;MACTV,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCE,KAAK,EAAE,OAAO;QACdQ,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;MACb,CAAE;MAAAf,QAAA,EAEDzE,OAAO,gBACNZ,OAAA;QAAK6F,SAAS,EAAC,kBAAkB;QAAAR,QAAA,gBAC/BrF,OAAA,CAACX,OAAO;UAACgH,SAAS,EAAC,QAAQ;UAACC,OAAO,EAAC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CjG,OAAA;UAAK6F,SAAS,EAAC,MAAM;UAAAR,QAAA,EAAC;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,gBAENjG,OAAA,CAAAE,SAAA;QAAAmF,QAAA,GAEG7E,kBAAkB,iBACjBR,OAAA;UAAK6F,SAAS,EAAC,MAAM;UAAAR,QAAA,gBACnBrF,OAAA;YAAI6F,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAC;UAAyB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDjG,OAAA,CAACb,IAAI;YACH0G,SAAS,EAAC,kCAAkC;YAC5CL,KAAK,EAAE;cACLC,eAAe,EAAE,wBAAwB;cACzCC,WAAW,EAAE,SAAS;cACtBa,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEFrF,OAAA,CAACb,IAAI,CAAC+G,IAAI;cAACL,SAAS,EAAC,MAAM;cAAAR,QAAA,eACzBrF,OAAA;gBAAK6F,SAAS,EAAC,mDAAmD;gBAAAR,QAAA,gBAChErF,OAAA;kBAAK6F,SAAS,EAAC,2BAA2B;kBAAAR,QAAA,gBACxCrF,OAAA,CAACN,OAAO;oBAACmG,SAAS,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzCjG,OAAA;oBAAM6F,SAAS,EAAC,sBAAsB;oBAAAR,QAAA,EAAC;kBAAO;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNjG,OAAA,CAACd,MAAM;kBACLoH,OAAO,EAAC,gBAAgB;kBACxBnB,IAAI,EAAC,IAAI;kBACTqB,OAAO,EAAEtB,qBAAsB;kBAC/BuB,QAAQ,EAAEzF,QAAS;kBAAAqE,QAAA,gBAEnBrF,OAAA,CAACP,OAAO;oBAACoG,SAAS,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGDjG,OAAA;UAAI6F,SAAS,EAAC,MAAM;UAAAR,QAAA,GAAC,sBAEnB,eAAArF,OAAA;YAAM6F,SAAS,EAAC,YAAY;YAACL,KAAK,EAAE;cAACG,KAAK,EAAE;YAAuB,CAAE;YAAAN,QAAA,GAAC,GACnE,EAAC3E,UAAU,CAAC0D,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAChB,OAAO,CAAC,CAACtB,MAAM,EAAC,UAAQ,EAACtB,UAAU,CAAC0D,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAAChB,OAAO,CAAC,CAACtB,MAAM,EAAC,iBAChG;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACJvF,UAAU,CAACsB,MAAM,KAAK,CAAC,gBACtBhC,OAAA;UAAK6F,SAAS,EAAC,kBAAkB;UAACL,KAAK,EAAE;YAACG,KAAK,EAAE;UAAuB,CAAE;UAAAN,QAAA,gBACxErF,OAAA,CAACR,KAAK;YAAC2F,IAAI,EAAE,EAAG;YAACU,SAAS,EAAC,MAAM;YAACL,KAAK,EAAE;cAACkB,OAAO,EAAE;YAAG;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3DjG,OAAA;YAAAqF,QAAA,EAAK;UAAuB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,gBAENjG,OAAA,CAAAE,SAAA;UAAAmF,QAAA,GAEG3E,UAAU,CAAC0D,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAChB,OAAO,CAAC,CAACtB,MAAM,GAAG,CAAC,iBAC3ChC,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAR,QAAA,EAC1B3E,UAAU,CAAC0D,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAChB,OAAO,CAAC,CAACN,GAAG,CAAE+B,SAAS,iBAC/C/E,OAAA;cAAyB6F,SAAS,EAAC,QAAQ;cAAAR,QAAA,eACzCrF,OAAA,CAACb,IAAI;gBACH0G,SAAS,EAAE,kBAAkBrF,kBAAkB,KAAKuE,SAAS,CAAC3C,GAAG,GAAG,SAAS,GAAG,EAAE,EAAG;gBACrFoD,KAAK,EAAE;kBACLC,eAAe,EAAEjF,kBAAkB,KAAKuE,SAAS,CAAC3C,GAAG,GAAG,wBAAwB,GAAG,uBAAuB;kBAC1GsD,WAAW,EAAElF,kBAAkB,KAAKuE,SAAS,CAAC3C,GAAG,GAAG,SAAS,GAAG,uBAAuB;kBACvFuE,MAAM,EAAE,SAAS;kBACjBC,UAAU,EAAE;gBACd,CAAE;gBACFJ,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAACC,SAAS,CAAE;gBAAAM,QAAA,eAE/CrF,OAAA,CAACb,IAAI,CAAC+G,IAAI;kBAACL,SAAS,EAAC,MAAM;kBAAAR,QAAA,eACzBrF,OAAA;oBAAK6F,SAAS,EAAC,kDAAkD;oBAAAR,QAAA,eAC/DrF,OAAA;sBAAK6F,SAAS,EAAC,aAAa;sBAAAR,QAAA,gBAC1BrF,OAAA;wBAAK6F,SAAS,EAAC,gCAAgC;wBAAAR,QAAA,gBAC7CrF,OAAA,CAACR,KAAK;0BAACqG,SAAS,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACvCjG,OAAA;0BAAI6F,SAAS,EAAC,cAAc;0BAAAR,QAAA,EAAEN,SAAS,CAAC1C;wBAAI;0BAAAyD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,EACjDzF,kBAAkB,KAAKuE,SAAS,CAAC3C,GAAG,iBACnCpC,OAAA,CAACZ,KAAK;0BAACyH,EAAE,EAAC,SAAS;0BAAChB,SAAS,EAAC,MAAM;0BAAAR,QAAA,EAAC;wBAAO;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CACpD,eACDjG,OAAA,CAACZ,KAAK;0BAACyH,EAAE,EAAC,SAAS;0BAAChB,SAAS,EAAC,MAAM;0BAAAR,QAAA,EAAC;wBAAS;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CAAC,eAENjG,OAAA;wBAAG6F,SAAS,EAAC,YAAY;wBAACL,KAAK,EAAE;0BAACG,KAAK,EAAE;wBAAuB,CAAE;wBAAAN,QAAA,EAAEN,SAAS,CAACzC;sBAAW;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAE9FjG,OAAA;wBAAK6F,SAAS,EAAC,mDAAmD;wBAAAR,QAAA,gBAChErF,OAAA;0BAAAqF,QAAA,eACErF,OAAA;4BAAM6F,SAAS,EAAC,sBAAsB;4BAAAR,QAAA,GAAC,OAChC,EAACxF,KAAK,CAACmF,cAAc,CAACD,SAAS,CAACvB,QAAQ,CAAC;0BAAA;4BAAAsC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eAENjG,OAAA;0BAAK6F,SAAS,EAAC,UAAU;0BAAAR,QAAA,eACvBrF,OAAA;4BAAK6F,SAAS,EAAC,OAAO;4BAAAR,QAAA,GACnBN,SAAS,CAACtC,cAAc,iBACvBzC,OAAA;8BAAK6F,SAAS,EAAC,cAAc;8BAAAR,QAAA,GAAC,OACvB,EAACxF,KAAK,CAACmF,cAAc,CAACD,SAAS,CAACtC,cAAc,CAAC,EAAC,SACvD;4BAAA;8BAAAqD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN,EACAlB,SAAS,CAACrC,WAAW,iBACpB1C,OAAA;8BAAKwF,KAAK,EAAE;gCAACG,KAAK,EAAE;8BAAuB,CAAE;8BAAAN,QAAA,GAAC,OACvC,EAACxF,KAAK,CAACmF,cAAc,CAACD,SAAS,CAACrC,WAAW,CAAC;4BAAA;8BAAAoD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9C,CACN,EACAlB,SAAS,CAACpC,UAAU,iBACnB3C,OAAA;8BAAK6F,SAAS,EAAC,cAAc;8BAAAR,QAAA,GAAC,WACnB,EAAC,IAAI1B,IAAI,CAACoB,SAAS,CAACpC,UAAU,CAAC,CAACmE,kBAAkB,CAAC,CAAC,EAAC,SAChE;4BAAA;8BAAAhB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC,GAvDClB,SAAS,CAAC3C,GAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwDlB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGAvF,UAAU,CAAC0D,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAAChB,OAAO,CAAC,CAACtB,MAAM,GAAG,CAAC,iBAC5ChC,OAAA,CAAAE,SAAA;YAAAmF,QAAA,gBACErF,OAAA;cAAI6F,SAAS,EAAC,mBAAmB;cAAAR,QAAA,GAAC,iBACjB,EAAC3E,UAAU,CAAC0D,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAAChB,OAAO,CAAC,CAACtB,MAAM,EAAC,GAC5D;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjG,OAAA;cAAK6F,SAAS,EAAC,SAAS;cAAAR,QAAA,EACrB3E,UAAU,CAAC0D,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAAChB,OAAO,CAAC,CAACN,GAAG,CAAE+B,SAAS,iBAChD/E,OAAA;gBAAyB6F,SAAS,EAAC,QAAQ;gBAAAR,QAAA,eACzCrF,OAAA,CAACb,IAAI;kBACH0G,SAAS,EAAC,yBAAyB;kBACnCL,KAAK,EAAE;oBACLC,eAAe,EAAE,wBAAwB;oBACzCC,WAAW,EAAE,wBAAwB;oBACrCiB,MAAM,EAAE,aAAa;oBACrBD,OAAO,EAAE,GAAG;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAvB,QAAA,eAEFrF,OAAA,CAACb,IAAI,CAAC+G,IAAI;oBAACL,SAAS,EAAC,MAAM;oBAAAR,QAAA,eACzBrF,OAAA;sBAAK6F,SAAS,EAAC,kDAAkD;sBAAAR,QAAA,eAC/DrF,OAAA;wBAAK6F,SAAS,EAAC,aAAa;wBAAAR,QAAA,gBAC1BrF,OAAA;0BAAK6F,SAAS,EAAC,gCAAgC;0BAAAR,QAAA,gBAC7CrF,OAAA,CAACR,KAAK;4BAACqG,SAAS,EAAC;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCjG,OAAA;4BAAI6F,SAAS,EAAC,cAAc;4BAAAR,QAAA,EAAEN,SAAS,CAAC1C;0BAAI;4BAAAyD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAClDjG,OAAA,CAACZ,KAAK;4BAACyH,EAAE,EAAC,SAAS;4BAAChB,SAAS,EAAC,MAAM;4BAACL,KAAK,EAAE;8BAACG,KAAK,EAAE;4BAAO,CAAE;4BAAAN,QAAA,EAAC;0BAAa;4BAAAS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChF,CAAC,eAENjG,OAAA;0BAAG6F,SAAS,EAAC,YAAY;0BAACL,KAAK,EAAE;4BAACG,KAAK,EAAE;0BAAuB,CAAE;0BAAAN,QAAA,EAAEN,SAAS,CAACzC;wBAAW;0BAAAwD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAE9FjG,OAAA;0BAAK6F,SAAS,EAAC,mDAAmD;0BAAAR,QAAA,gBAChErF,OAAA;4BAAAqF,QAAA,eACErF,OAAA;8BAAM6F,SAAS,EAAC,4BAA4B;8BAAAR,QAAA,EACzCN,SAAS,CAAC5C;4BAAO;8BAAA2D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACd;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eAENjG,OAAA;4BAAK6F,SAAS,EAAC,UAAU;4BAAAR,QAAA,eACvBrF,OAAA;8BAAK6F,SAAS,EAAC,OAAO;8BAAAR,QAAA,GACnBN,SAAS,CAACtC,cAAc,iBACvBzC,OAAA;gCAAK6F,SAAS,EAAE,GAAGvF,UAAU,IAAIyE,SAAS,CAACtC,cAAc,GAAG,cAAc,GAAG,cAAc,EAAG;gCAAA4C,QAAA,GAAC,OACxF,EAACxF,KAAK,CAACmF,cAAc,CAACD,SAAS,CAACtC,cAAc,CAAC,EACnDnC,UAAU,IAAIyE,SAAS,CAACtC,cAAc,GAAG,IAAI,GAAG,IAAI;8BAAA;gCAAAqD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClD,CACN,EACAlB,SAAS,CAACrC,WAAW,iBACpB1C,OAAA;gCAAKwF,KAAK,EAAE;kCAACG,KAAK,EAAE;gCAAuB,CAAE;gCAAAN,QAAA,GAAC,OACvC,EAACxF,KAAK,CAACmF,cAAc,CAACD,SAAS,CAACrC,WAAW,CAAC;8BAAA;gCAAAoD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9C,CACN,EACA,CAAClB,SAAS,CAACnB,SAAS,IAAImB,SAAS,CAACpC,UAAU,kBAC3C3C,OAAA;gCAAK6F,SAAS,EAAC,cAAc;gCAAAR,QAAA,GAAC,UACpB,EAAC,IAAI1B,IAAI,CAACoB,SAAS,CAACnB,SAAS,IAAImB,SAAS,CAACpC,UAAU,CAAC,CAACmE,kBAAkB,CAAC,CAAC;8BAAA;gCAAAhB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChF,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC,GArDClB,SAAS,CAAC3C,GAAG;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsDlB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,eACN,CACH;QAAA,eACD,CACH;MAAA,eACD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAEbjG,OAAA,CAACf,KAAK,CAAC8H,MAAM;MACXvB,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE;MACf,CAAE;MAAAL,QAAA,eAEFrF,OAAA,CAACd,MAAM;QAACoH,OAAO,EAAC,eAAe;QAACE,OAAO,EAAEnG,MAAO;QAACoG,QAAQ,EAAEzF,QAAS;QAAAqE,QAAA,EAAC;MAErE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;AAACxF,EAAA,CAjdIN,cAAc;AAAA6G,EAAA,GAAd7G,cAAc;AAmdpB,eAAeA,cAAc;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}