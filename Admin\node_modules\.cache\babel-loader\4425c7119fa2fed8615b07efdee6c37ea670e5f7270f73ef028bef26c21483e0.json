{"ast": null, "code": "function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport { useCallback, useRef, useState } from 'react';\nexport function defaultKey(key) {\n  return 'default' + key.charAt(0).toUpperCase() + key.substr(1);\n}\nfunction useUncontrolledProp(propValue, defaultValue, handler) {\n  const wasPropRef = useRef(propValue !== undefined);\n  const [stateValue, setState] = useState(defaultValue);\n  const isProp = propValue !== undefined;\n  const wasProp = wasPropRef.current;\n  wasPropRef.current = isProp;\n\n  /**\n   * If a prop switches from controlled to Uncontrolled\n   * reset its value to the defaultValue\n   */\n  if (!isProp && wasProp && stateValue !== defaultValue) {\n    setState(defaultValue);\n  }\n  return [isProp ? propValue : stateValue, useCallback((...args) => {\n    const [value, ...rest] = args;\n    let returnValue = handler == null ? void 0 : handler(value, ...rest);\n    setState(value);\n    return returnValue;\n  }, [handler])];\n}\nexport { useUncontrolledProp };\nexport function useUncontrolled(props, config) {\n  return Object.keys(config).reduce((result, fieldName) => {\n    const _ref = result,\n      _defaultKey = defaultKey(fieldName),\n      {\n        [_defaultKey]: defaultValue,\n        [fieldName]: propsValue\n      } = _ref,\n      rest = _objectWithoutPropertiesLoose(_ref, [_defaultKey, fieldName].map(_toPropertyKey));\n    const handlerName = config[fieldName];\n    const [value, handler] = useUncontrolledProp(propsValue, defaultValue, props[handlerName]);\n    return Object.assign({}, rest, {\n      [fieldName]: value,\n      [handlerName]: handler\n    });\n  }, props);\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "arg", "_toPrimitive", "String", "input", "hint", "prim", "Symbol", "toPrimitive", "undefined", "res", "call", "TypeError", "Number", "useCallback", "useRef", "useState", "defaultKey", "char<PERSON>t", "toUpperCase", "substr", "useUncontrolledProp", "propValue", "defaultValue", "handler", "wasPropRef", "stateValue", "setState", "isProp", "wasProp", "current", "args", "value", "rest", "returnValue", "useUncontrolled", "props", "config", "reduce", "result", "fieldName", "_ref", "_default<PERSON>ey", "props<PERSON><PERSON><PERSON>", "map", "handler<PERSON>ame", "assign"], "sources": ["E:/Uroom/Admin/node_modules/@restart/ui/node_modules/uncontrollable/lib/esm/index.js"], "sourcesContent": ["function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { useCallback, useRef, useState } from 'react';\nexport function defaultKey(key) {\n  return 'default' + key.charAt(0).toUpperCase() + key.substr(1);\n}\nfunction useUncontrolledProp(propValue, defaultValue, handler) {\n  const wasPropRef = useRef(propValue !== undefined);\n  const [stateValue, setState] = useState(defaultValue);\n  const isProp = propValue !== undefined;\n  const wasProp = wasPropRef.current;\n  wasPropRef.current = isProp;\n\n  /**\n   * If a prop switches from controlled to Uncontrolled\n   * reset its value to the defaultValue\n   */\n  if (!isProp && wasProp && stateValue !== defaultValue) {\n    setState(defaultValue);\n  }\n  return [isProp ? propValue : stateValue, useCallback((...args) => {\n    const [value, ...rest] = args;\n    let returnValue = handler == null ? void 0 : handler(value, ...rest);\n    setState(value);\n    return returnValue;\n  }, [handler])];\n}\nexport { useUncontrolledProp };\nexport function useUncontrolled(props, config) {\n  return Object.keys(config).reduce((result, fieldName) => {\n    const _ref = result,\n      _defaultKey = defaultKey(fieldName),\n      {\n        [_defaultKey]: defaultValue,\n        [fieldName]: propsValue\n      } = _ref,\n      rest = _objectWithoutPropertiesLoose(_ref, [_defaultKey, fieldName].map(_toPropertyKey));\n    const handlerName = config[fieldName];\n    const [value, handler] = useUncontrolledProp(propsValue, defaultValue, props[handlerName]);\n    return Object.assign({}, rest, {\n      [fieldName]: value,\n      [handlerName]: handler\n    });\n  }, props);\n}"], "mappings": "AAAA,SAASA,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EAAE,IAAIM,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGH,UAAU,CAACI,CAAC,CAAC;IAAE,IAAIN,QAAQ,CAACQ,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAClT,SAASQ,cAAcA,CAACC,GAAG,EAAE;EAAE,IAAIL,GAAG,GAAGM,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO,OAAOL,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGO,MAAM,CAACP,GAAG,CAAC;AAAE;AAC1H,SAASM,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,WAAW,CAAC;EAAE,IAAIF,IAAI,KAAKG,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACP,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACP,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGU,MAAM,EAAET,KAAK,CAAC;AAAE;AACxX,SAASU,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrD,OAAO,SAASC,UAAUA,CAACrB,GAAG,EAAE;EAC9B,OAAO,SAAS,GAAGA,GAAG,CAACsB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvB,GAAG,CAACwB,MAAM,CAAC,CAAC,CAAC;AAChE;AACA,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAAE;EAC7D,MAAMC,UAAU,GAAGV,MAAM,CAACO,SAAS,KAAKb,SAAS,CAAC;EAClD,MAAM,CAACiB,UAAU,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAACO,YAAY,CAAC;EACrD,MAAMK,MAAM,GAAGN,SAAS,KAAKb,SAAS;EACtC,MAAMoB,OAAO,GAAGJ,UAAU,CAACK,OAAO;EAClCL,UAAU,CAACK,OAAO,GAAGF,MAAM;;EAE3B;AACF;AACA;AACA;EACE,IAAI,CAACA,MAAM,IAAIC,OAAO,IAAIH,UAAU,KAAKH,YAAY,EAAE;IACrDI,QAAQ,CAACJ,YAAY,CAAC;EACxB;EACA,OAAO,CAACK,MAAM,GAAGN,SAAS,GAAGI,UAAU,EAAEZ,WAAW,CAAC,CAAC,GAAGiB,IAAI,KAAK;IAChE,MAAM,CAACC,KAAK,EAAE,GAAGC,IAAI,CAAC,GAAGF,IAAI;IAC7B,IAAIG,WAAW,GAAGV,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,KAAK,EAAE,GAAGC,IAAI,CAAC;IACpEN,QAAQ,CAACK,KAAK,CAAC;IACf,OAAOE,WAAW;EACpB,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC,CAAC;AAChB;AACA,SAASH,mBAAmB;AAC5B,OAAO,SAASc,eAAeA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC7C,OAAO3C,MAAM,CAACC,IAAI,CAAC0C,MAAM,CAAC,CAACC,MAAM,CAAC,CAACC,MAAM,EAAEC,SAAS,KAAK;IACvD,MAAMC,IAAI,GAAGF,MAAM;MACjBG,WAAW,GAAGzB,UAAU,CAACuB,SAAS,CAAC;MACnC;QACE,CAACE,WAAW,GAAGnB,YAAY;QAC3B,CAACiB,SAAS,GAAGG;MACf,CAAC,GAAGF,IAAI;MACRR,IAAI,GAAG5C,6BAA6B,CAACoD,IAAI,EAAE,CAACC,WAAW,EAAEF,SAAS,CAAC,CAACI,GAAG,CAAC5C,cAAc,CAAC,CAAC;IAC1F,MAAM6C,WAAW,GAAGR,MAAM,CAACG,SAAS,CAAC;IACrC,MAAM,CAACR,KAAK,EAAER,OAAO,CAAC,GAAGH,mBAAmB,CAACsB,UAAU,EAAEpB,YAAY,EAAEa,KAAK,CAACS,WAAW,CAAC,CAAC;IAC1F,OAAOnD,MAAM,CAACoD,MAAM,CAAC,CAAC,CAAC,EAAEb,IAAI,EAAE;MAC7B,CAACO,SAAS,GAAGR,KAAK;MAClB,CAACa,WAAW,GAAGrB;IACjB,CAAC,CAAC;EACJ,CAAC,EAAEY,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}