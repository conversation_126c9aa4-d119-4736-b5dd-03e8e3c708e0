{"ast": null, "code": "import { useCallback } from 'react';\nimport useCommittedRef from './useCommittedRef';\nexport default function useEventCallback(fn) {\n  const ref = useCommittedRef(fn);\n  return useCallback(function (...args) {\n    return ref.current && ref.current(...args);\n  }, [ref]);\n}", "map": {"version": 3, "names": ["useCallback", "useCommittedRef", "useEventCallback", "fn", "ref", "args", "current"], "sources": ["E:/Uroom/Admin/node_modules/@restart/hooks/esm/useEventCallback.js"], "sourcesContent": ["import { useCallback } from 'react';\nimport useCommittedRef from './useCommittedRef';\nexport default function useEventCallback(fn) {\n  const ref = useCommittedRef(fn);\n  return useCallback(function (...args) {\n    return ref.current && ref.current(...args);\n  }, [ref]);\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,eAAe,SAASC,gBAAgBA,CAACC,EAAE,EAAE;EAC3C,MAAMC,GAAG,GAAGH,eAAe,CAACE,EAAE,CAAC;EAC/B,OAAOH,WAAW,CAAC,UAAU,GAAGK,IAAI,EAAE;IACpC,OAAOD,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACE,OAAO,CAAC,GAAGD,IAAI,CAAC;EAC5C,CAAC,EAAE,CAACD,GAAG,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}