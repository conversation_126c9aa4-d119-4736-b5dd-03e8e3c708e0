{"ast": null, "code": "'use strict';\n\nvar path = require('../internals/path');\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\nvar aFunction = function (variable) {\n  return isCallable(variable) ? variable : undefined;\n};\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(globalThis[namespace]) : path[namespace] && path[namespace][method] || globalThis[namespace] && globalThis[namespace][method];\n};", "map": {"version": 3, "names": ["path", "require", "globalThis", "isCallable", "aFunction", "variable", "undefined", "module", "exports", "namespace", "method", "arguments", "length"], "sources": ["E:/Uroom/Admin/node_modules/core-js-pure/internals/get-built-in.js"], "sourcesContent": ["'use strict';\nvar path = require('../internals/path');\nvar globalThis = require('../internals/global-this');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (variable) {\n  return isCallable(variable) ? variable : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(globalThis[namespace])\n    : path[namespace] && path[namespace][method] || globalThis[namespace] && globalThis[namespace][method];\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AACvC,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIG,SAAS,GAAG,SAAAA,CAAUC,QAAQ,EAAE;EAClC,OAAOF,UAAU,CAACE,QAAQ,CAAC,GAAGA,QAAQ,GAAGC,SAAS;AACpD,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;EAC5C,OAAOC,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGR,SAAS,CAACJ,IAAI,CAACS,SAAS,CAAC,CAAC,IAAIL,SAAS,CAACF,UAAU,CAACO,SAAS,CAAC,CAAC,GACxFT,IAAI,CAACS,SAAS,CAAC,IAAIT,IAAI,CAACS,SAAS,CAAC,CAACC,MAAM,CAAC,IAAIR,UAAU,CAACO,SAAS,CAAC,IAAIP,UAAU,CAACO,SAAS,CAAC,CAACC,MAAM,CAAC;AAC1G,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}