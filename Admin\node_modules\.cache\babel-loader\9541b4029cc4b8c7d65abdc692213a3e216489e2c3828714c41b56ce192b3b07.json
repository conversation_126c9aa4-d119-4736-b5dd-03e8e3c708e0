{"ast": null, "code": "import css from 'dom-helpers/css';\nimport transitionEnd from 'dom-helpers/transitionEnd';\nfunction parseDuration(node, property) {\n  const str = css(node, property) || '';\n  const mult = str.indexOf('ms') === -1 ? 1000 : 1;\n  return parseFloat(str) * mult;\n}\nexport default function transitionEndListener(element, handler) {\n  const duration = parseDuration(element, 'transitionDuration');\n  const delay = parseDuration(element, 'transitionDelay');\n  const remove = transitionEnd(element, e => {\n    if (e.target === element) {\n      remove();\n      handler(e);\n    }\n  }, duration + delay);\n}", "map": {"version": 3, "names": ["css", "transitionEnd", "parseDuration", "node", "property", "str", "mult", "indexOf", "parseFloat", "transitionEndListener", "element", "handler", "duration", "delay", "remove", "e", "target"], "sources": ["E:/Uroom/Admin/node_modules/react-bootstrap/esm/transitionEndListener.js"], "sourcesContent": ["import css from 'dom-helpers/css';\nimport transitionEnd from 'dom-helpers/transitionEnd';\nfunction parseDuration(node, property) {\n  const str = css(node, property) || '';\n  const mult = str.indexOf('ms') === -1 ? 1000 : 1;\n  return parseFloat(str) * mult;\n}\nexport default function transitionEndListener(element, handler) {\n  const duration = parseDuration(element, 'transitionDuration');\n  const delay = parseDuration(element, 'transitionDelay');\n  const remove = transitionEnd(element, e => {\n    if (e.target === element) {\n      remove();\n      handler(e);\n    }\n  }, duration + delay);\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,iBAAiB;AACjC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,SAASC,aAAaA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACrC,MAAMC,GAAG,GAAGL,GAAG,CAACG,IAAI,EAAEC,QAAQ,CAAC,IAAI,EAAE;EACrC,MAAME,IAAI,GAAGD,GAAG,CAACE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;EAChD,OAAOC,UAAU,CAACH,GAAG,CAAC,GAAGC,IAAI;AAC/B;AACA,eAAe,SAASG,qBAAqBA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC9D,MAAMC,QAAQ,GAAGV,aAAa,CAACQ,OAAO,EAAE,oBAAoB,CAAC;EAC7D,MAAMG,KAAK,GAAGX,aAAa,CAACQ,OAAO,EAAE,iBAAiB,CAAC;EACvD,MAAMI,MAAM,GAAGb,aAAa,CAACS,OAAO,EAAEK,CAAC,IAAI;IACzC,IAAIA,CAAC,CAACC,MAAM,KAAKN,OAAO,EAAE;MACxBI,MAAM,CAAC,CAAC;MACRH,OAAO,CAACI,CAAC,CAAC;IACZ;EACF,CAAC,EAAEH,QAAQ,GAAGC,KAAK,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}