{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nvar _jsxFileName = \"/Users/<USER>/src/uncontrollable/src/uncontrollable.js\";\nimport React from 'react';\nimport { polyfill } from 'react-lifecycles-compat';\nimport invariant from 'invariant';\nimport * as Utils from './utils';\nexport default function uncontrollable(Component, controlledValues, methods) {\n  if (methods === void 0) {\n    methods = [];\n  }\n  var displayName = Component.displayName || Component.name || 'Component';\n  var canAcceptRef = Utils.canAcceptRef(Component);\n  var controlledProps = Object.keys(controlledValues);\n  var PROPS_TO_OMIT = controlledProps.map(Utils.defaultKey);\n  !(canAcceptRef || !methods.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, '[uncontrollable] stateless function components cannot pass through methods ' + 'because they have no associated instances. Check component: ' + displayName + ', ' + 'attempting to pass through methods: ' + methods.join(', ')) : invariant(false) : void 0;\n  var UncontrolledComponent = /*#__PURE__*/\n  function (_React$Component) {\n    _inheritsLoose(UncontrolledComponent, _React$Component);\n    function UncontrolledComponent() {\n      var _this;\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n      _this.handlers = Object.create(null);\n      controlledProps.forEach(function (propName) {\n        var handlerName = controlledValues[propName];\n        var handleChange = function handleChange(value) {\n          if (_this.props[handlerName]) {\n            var _this$props;\n            _this._notifying = true;\n            for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n              args[_key2 - 1] = arguments[_key2];\n            }\n            (_this$props = _this.props)[handlerName].apply(_this$props, [value].concat(args));\n            _this._notifying = false;\n          }\n          if (!_this.unmounted) _this.setState(function (_ref) {\n            var _extends2;\n            var values = _ref.values;\n            return {\n              values: _extends(Object.create(null), values, (_extends2 = {}, _extends2[propName] = value, _extends2))\n            };\n          });\n        };\n        _this.handlers[handlerName] = handleChange;\n      });\n      if (methods.length) _this.attachRef = function (ref) {\n        _this.inner = ref;\n      };\n      var values = Object.create(null);\n      controlledProps.forEach(function (key) {\n        values[key] = _this.props[Utils.defaultKey(key)];\n      });\n      _this.state = {\n        values: values,\n        prevProps: {}\n      };\n      return _this;\n    }\n    var _proto = UncontrolledComponent.prototype;\n    _proto.shouldComponentUpdate = function shouldComponentUpdate() {\n      //let setState trigger the update\n      return !this._notifying;\n    };\n    UncontrolledComponent.getDerivedStateFromProps = function getDerivedStateFromProps(props, _ref2) {\n      var values = _ref2.values,\n        prevProps = _ref2.prevProps;\n      var nextState = {\n        values: _extends(Object.create(null), values),\n        prevProps: {}\n      };\n      controlledProps.forEach(function (key) {\n        /**\n         * If a prop switches from controlled to Uncontrolled\n         * reset its value to the defaultValue\n         */\n        nextState.prevProps[key] = props[key];\n        if (!Utils.isProp(props, key) && Utils.isProp(prevProps, key)) {\n          nextState.values[key] = props[Utils.defaultKey(key)];\n        }\n      });\n      return nextState;\n    };\n    _proto.componentWillUnmount = function componentWillUnmount() {\n      this.unmounted = true;\n    };\n    _proto.render = function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        innerRef = _this$props2.innerRef,\n        props = _objectWithoutPropertiesLoose(_this$props2, [\"innerRef\"]);\n      PROPS_TO_OMIT.forEach(function (prop) {\n        delete props[prop];\n      });\n      var newProps = {};\n      controlledProps.forEach(function (propName) {\n        var propValue = _this2.props[propName];\n        newProps[propName] = propValue !== undefined ? propValue : _this2.state.values[propName];\n      });\n      return React.createElement(Component, _extends({}, props, newProps, this.handlers, {\n        ref: innerRef || this.attachRef\n      }));\n    };\n    return UncontrolledComponent;\n  }(React.Component);\n  polyfill(UncontrolledComponent);\n  UncontrolledComponent.displayName = \"Uncontrolled(\" + displayName + \")\";\n  UncontrolledComponent.propTypes = _extends({\n    innerRef: function innerRef() {}\n  }, Utils.uncontrolledPropTypes(controlledValues, displayName));\n  methods.forEach(function (method) {\n    UncontrolledComponent.prototype[method] = function $proxiedMethod() {\n      var _this$inner;\n      return (_this$inner = this.inner)[method].apply(_this$inner, arguments);\n    };\n  });\n  var WrappedComponent = UncontrolledComponent;\n  if (React.forwardRef) {\n    WrappedComponent = React.forwardRef(function (props, ref) {\n      return React.createElement(UncontrolledComponent, _extends({}, props, {\n        innerRef: ref,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 128\n        },\n        __self: this\n      }));\n    });\n    WrappedComponent.propTypes = UncontrolledComponent.propTypes;\n  }\n  WrappedComponent.ControlledComponent = Component;\n  /**\n   * useful when wrapping a Component and you want to control\n   * everything\n   */\n\n  WrappedComponent.deferControlTo = function (newComponent, additions, nextMethods) {\n    if (additions === void 0) {\n      additions = {};\n    }\n    return uncontrollable(newComponent, _extends({}, controlledValues, additions), nextMethods);\n  };\n  return WrappedComponent;\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_inherits<PERSON><PERSON>e", "_jsxFileName", "React", "polyfill", "invariant", "Utils", "uncontrollable", "Component", "controlledValues", "methods", "displayName", "name", "canAcceptRef", "controlledProps", "Object", "keys", "PROPS_TO_OMIT", "map", "defaultKey", "length", "process", "env", "NODE_ENV", "join", "UncontrolledComponent", "_React$Component", "_this", "_len", "arguments", "args", "Array", "_key", "call", "apply", "concat", "handlers", "create", "for<PERSON>ach", "propName", "handler<PERSON>ame", "handleChange", "value", "props", "_this$props", "_notifying", "_len2", "_key2", "unmounted", "setState", "_ref", "_extends2", "values", "attachRef", "ref", "inner", "key", "state", "prevProps", "_proto", "prototype", "shouldComponentUpdate", "getDerivedStateFromProps", "_ref2", "nextState", "isProp", "componentWillUnmount", "render", "_this2", "_this$props2", "innerRef", "prop", "newProps", "propValue", "undefined", "createElement", "propTypes", "uncontrolledPropTypes", "method", "$proxiedMethod", "_this$inner", "WrappedComponent", "forwardRef", "__source", "fileName", "lineNumber", "__self", "ControlledComponent", "deferControlTo", "newComponent", "additions", "nextMethods"], "sources": ["E:/Uroom/Admin/node_modules/uncontrollable/lib/esm/uncontrollable.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nvar _jsxFileName = \"/Users/<USER>/src/uncontrollable/src/uncontrollable.js\";\nimport React from 'react';\nimport { polyfill } from 'react-lifecycles-compat';\nimport invariant from 'invariant';\nimport * as Utils from './utils';\nexport default function uncontrollable(Component, controlledValues, methods) {\n  if (methods === void 0) {\n    methods = [];\n  }\n\n  var displayName = Component.displayName || Component.name || 'Component';\n  var canAcceptRef = Utils.canAcceptRef(Component);\n  var controlledProps = Object.keys(controlledValues);\n  var PROPS_TO_OMIT = controlledProps.map(Utils.defaultKey);\n  !(canAcceptRef || !methods.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, '[uncontrollable] stateless function components cannot pass through methods ' + 'because they have no associated instances. Check component: ' + displayName + ', ' + 'attempting to pass through methods: ' + methods.join(', ')) : invariant(false) : void 0;\n\n  var UncontrolledComponent =\n  /*#__PURE__*/\n  function (_React$Component) {\n    _inheritsLoose(UncontrolledComponent, _React$Component);\n\n    function UncontrolledComponent() {\n      var _this;\n\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n      _this.handlers = Object.create(null);\n      controlledProps.forEach(function (propName) {\n        var handlerName = controlledValues[propName];\n\n        var handleChange = function handleChange(value) {\n          if (_this.props[handlerName]) {\n            var _this$props;\n\n            _this._notifying = true;\n\n            for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n              args[_key2 - 1] = arguments[_key2];\n            }\n\n            (_this$props = _this.props)[handlerName].apply(_this$props, [value].concat(args));\n\n            _this._notifying = false;\n          }\n\n          if (!_this.unmounted) _this.setState(function (_ref) {\n            var _extends2;\n\n            var values = _ref.values;\n            return {\n              values: _extends(Object.create(null), values, (_extends2 = {}, _extends2[propName] = value, _extends2))\n            };\n          });\n        };\n\n        _this.handlers[handlerName] = handleChange;\n      });\n      if (methods.length) _this.attachRef = function (ref) {\n        _this.inner = ref;\n      };\n      var values = Object.create(null);\n      controlledProps.forEach(function (key) {\n        values[key] = _this.props[Utils.defaultKey(key)];\n      });\n      _this.state = {\n        values: values,\n        prevProps: {}\n      };\n      return _this;\n    }\n\n    var _proto = UncontrolledComponent.prototype;\n\n    _proto.shouldComponentUpdate = function shouldComponentUpdate() {\n      //let setState trigger the update\n      return !this._notifying;\n    };\n\n    UncontrolledComponent.getDerivedStateFromProps = function getDerivedStateFromProps(props, _ref2) {\n      var values = _ref2.values,\n          prevProps = _ref2.prevProps;\n      var nextState = {\n        values: _extends(Object.create(null), values),\n        prevProps: {}\n      };\n      controlledProps.forEach(function (key) {\n        /**\n         * If a prop switches from controlled to Uncontrolled\n         * reset its value to the defaultValue\n         */\n        nextState.prevProps[key] = props[key];\n\n        if (!Utils.isProp(props, key) && Utils.isProp(prevProps, key)) {\n          nextState.values[key] = props[Utils.defaultKey(key)];\n        }\n      });\n      return nextState;\n    };\n\n    _proto.componentWillUnmount = function componentWillUnmount() {\n      this.unmounted = true;\n    };\n\n    _proto.render = function render() {\n      var _this2 = this;\n\n      var _this$props2 = this.props,\n          innerRef = _this$props2.innerRef,\n          props = _objectWithoutPropertiesLoose(_this$props2, [\"innerRef\"]);\n\n      PROPS_TO_OMIT.forEach(function (prop) {\n        delete props[prop];\n      });\n      var newProps = {};\n      controlledProps.forEach(function (propName) {\n        var propValue = _this2.props[propName];\n        newProps[propName] = propValue !== undefined ? propValue : _this2.state.values[propName];\n      });\n      return React.createElement(Component, _extends({}, props, newProps, this.handlers, {\n        ref: innerRef || this.attachRef\n      }));\n    };\n\n    return UncontrolledComponent;\n  }(React.Component);\n\n  polyfill(UncontrolledComponent);\n  UncontrolledComponent.displayName = \"Uncontrolled(\" + displayName + \")\";\n  UncontrolledComponent.propTypes = _extends({\n    innerRef: function innerRef() {}\n  }, Utils.uncontrolledPropTypes(controlledValues, displayName));\n  methods.forEach(function (method) {\n    UncontrolledComponent.prototype[method] = function $proxiedMethod() {\n      var _this$inner;\n\n      return (_this$inner = this.inner)[method].apply(_this$inner, arguments);\n    };\n  });\n  var WrappedComponent = UncontrolledComponent;\n\n  if (React.forwardRef) {\n    WrappedComponent = React.forwardRef(function (props, ref) {\n      return React.createElement(UncontrolledComponent, _extends({}, props, {\n        innerRef: ref,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 128\n        },\n        __self: this\n      }));\n    });\n    WrappedComponent.propTypes = UncontrolledComponent.propTypes;\n  }\n\n  WrappedComponent.ControlledComponent = Component;\n  /**\n   * useful when wrapping a Component and you want to control\n   * everything\n   */\n\n  WrappedComponent.deferControlTo = function (newComponent, additions, nextMethods) {\n    if (additions === void 0) {\n      additions = {};\n    }\n\n    return uncontrollable(newComponent, _extends({}, controlledValues, additions), nextMethods);\n  };\n\n  return WrappedComponent;\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,IAAIC,YAAY,GAAG,yDAAyD;AAC5E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,SAAS;AAChC,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,gBAAgB,EAAEC,OAAO,EAAE;EAC3E,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,EAAE;EACd;EAEA,IAAIC,WAAW,GAAGH,SAAS,CAACG,WAAW,IAAIH,SAAS,CAACI,IAAI,IAAI,WAAW;EACxE,IAAIC,YAAY,GAAGP,KAAK,CAACO,YAAY,CAACL,SAAS,CAAC;EAChD,IAAIM,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACP,gBAAgB,CAAC;EACnD,IAAIQ,aAAa,GAAGH,eAAe,CAACI,GAAG,CAACZ,KAAK,CAACa,UAAU,CAAC;EACzD,EAAEN,YAAY,IAAI,CAACH,OAAO,CAACU,MAAM,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlB,SAAS,CAAC,KAAK,EAAE,6EAA6E,GAAG,8DAA8D,GAAGM,WAAW,GAAG,IAAI,GAAG,sCAAsC,GAAGD,OAAO,CAACc,IAAI,CAAC,IAAI,CAAC,CAAC,GAAGnB,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAE5V,IAAIoB,qBAAqB,GACzB;EACA,UAAUC,gBAAgB,EAAE;IAC1BzB,cAAc,CAACwB,qBAAqB,EAAEC,gBAAgB,CAAC;IAEvD,SAASD,qBAAqBA,CAAA,EAAG;MAC/B,IAAIE,KAAK;MAET,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACT,MAAM,EAAEU,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;MAC9B;MAEAL,KAAK,GAAGD,gBAAgB,CAACO,IAAI,CAACC,KAAK,CAACR,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAACS,MAAM,CAACL,IAAI,CAAC,CAAC,IAAI,IAAI;MAClFH,KAAK,CAACS,QAAQ,GAAGrB,MAAM,CAACsB,MAAM,CAAC,IAAI,CAAC;MACpCvB,eAAe,CAACwB,OAAO,CAAC,UAAUC,QAAQ,EAAE;QAC1C,IAAIC,WAAW,GAAG/B,gBAAgB,CAAC8B,QAAQ,CAAC;QAE5C,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;UAC9C,IAAIf,KAAK,CAACgB,KAAK,CAACH,WAAW,CAAC,EAAE;YAC5B,IAAII,WAAW;YAEfjB,KAAK,CAACkB,UAAU,GAAG,IAAI;YAEvB,KAAK,IAAIC,KAAK,GAAGjB,SAAS,CAACT,MAAM,EAAEU,IAAI,GAAG,IAAIC,KAAK,CAACe,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cACjHjB,IAAI,CAACiB,KAAK,GAAG,CAAC,CAAC,GAAGlB,SAAS,CAACkB,KAAK,CAAC;YACpC;YAEA,CAACH,WAAW,GAAGjB,KAAK,CAACgB,KAAK,EAAEH,WAAW,CAAC,CAACN,KAAK,CAACU,WAAW,EAAE,CAACF,KAAK,CAAC,CAACP,MAAM,CAACL,IAAI,CAAC,CAAC;YAEjFH,KAAK,CAACkB,UAAU,GAAG,KAAK;UAC1B;UAEA,IAAI,CAAClB,KAAK,CAACqB,SAAS,EAAErB,KAAK,CAACsB,QAAQ,CAAC,UAAUC,IAAI,EAAE;YACnD,IAAIC,SAAS;YAEb,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;YACxB,OAAO;cACLA,MAAM,EAAEpD,QAAQ,CAACe,MAAM,CAACsB,MAAM,CAAC,IAAI,CAAC,EAAEe,MAAM,GAAGD,SAAS,GAAG,CAAC,CAAC,EAAEA,SAAS,CAACZ,QAAQ,CAAC,GAAGG,KAAK,EAAES,SAAS,CAAC;YACxG,CAAC;UACH,CAAC,CAAC;QACJ,CAAC;QAEDxB,KAAK,CAACS,QAAQ,CAACI,WAAW,CAAC,GAAGC,YAAY;MAC5C,CAAC,CAAC;MACF,IAAI/B,OAAO,CAACU,MAAM,EAAEO,KAAK,CAAC0B,SAAS,GAAG,UAAUC,GAAG,EAAE;QACnD3B,KAAK,CAAC4B,KAAK,GAAGD,GAAG;MACnB,CAAC;MACD,IAAIF,MAAM,GAAGrC,MAAM,CAACsB,MAAM,CAAC,IAAI,CAAC;MAChCvB,eAAe,CAACwB,OAAO,CAAC,UAAUkB,GAAG,EAAE;QACrCJ,MAAM,CAACI,GAAG,CAAC,GAAG7B,KAAK,CAACgB,KAAK,CAACrC,KAAK,CAACa,UAAU,CAACqC,GAAG,CAAC,CAAC;MAClD,CAAC,CAAC;MACF7B,KAAK,CAAC8B,KAAK,GAAG;QACZL,MAAM,EAAEA,MAAM;QACdM,SAAS,EAAE,CAAC;MACd,CAAC;MACD,OAAO/B,KAAK;IACd;IAEA,IAAIgC,MAAM,GAAGlC,qBAAqB,CAACmC,SAAS;IAE5CD,MAAM,CAACE,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;MAC9D;MACA,OAAO,CAAC,IAAI,CAAChB,UAAU;IACzB,CAAC;IAEDpB,qBAAqB,CAACqC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACnB,KAAK,EAAEoB,KAAK,EAAE;MAC/F,IAAIX,MAAM,GAAGW,KAAK,CAACX,MAAM;QACrBM,SAAS,GAAGK,KAAK,CAACL,SAAS;MAC/B,IAAIM,SAAS,GAAG;QACdZ,MAAM,EAAEpD,QAAQ,CAACe,MAAM,CAACsB,MAAM,CAAC,IAAI,CAAC,EAAEe,MAAM,CAAC;QAC7CM,SAAS,EAAE,CAAC;MACd,CAAC;MACD5C,eAAe,CAACwB,OAAO,CAAC,UAAUkB,GAAG,EAAE;QACrC;AACR;AACA;AACA;QACQQ,SAAS,CAACN,SAAS,CAACF,GAAG,CAAC,GAAGb,KAAK,CAACa,GAAG,CAAC;QAErC,IAAI,CAAClD,KAAK,CAAC2D,MAAM,CAACtB,KAAK,EAAEa,GAAG,CAAC,IAAIlD,KAAK,CAAC2D,MAAM,CAACP,SAAS,EAAEF,GAAG,CAAC,EAAE;UAC7DQ,SAAS,CAACZ,MAAM,CAACI,GAAG,CAAC,GAAGb,KAAK,CAACrC,KAAK,CAACa,UAAU,CAACqC,GAAG,CAAC,CAAC;QACtD;MACF,CAAC,CAAC;MACF,OAAOQ,SAAS;IAClB,CAAC;IAEDL,MAAM,CAACO,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;MAC5D,IAAI,CAAClB,SAAS,GAAG,IAAI;IACvB,CAAC;IAEDW,MAAM,CAACQ,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;MAChC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIC,YAAY,GAAG,IAAI,CAAC1B,KAAK;QACzB2B,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChC3B,KAAK,GAAG5C,6BAA6B,CAACsE,YAAY,EAAE,CAAC,UAAU,CAAC,CAAC;MAErEpD,aAAa,CAACqB,OAAO,CAAC,UAAUiC,IAAI,EAAE;QACpC,OAAO5B,KAAK,CAAC4B,IAAI,CAAC;MACpB,CAAC,CAAC;MACF,IAAIC,QAAQ,GAAG,CAAC,CAAC;MACjB1D,eAAe,CAACwB,OAAO,CAAC,UAAUC,QAAQ,EAAE;QAC1C,IAAIkC,SAAS,GAAGL,MAAM,CAACzB,KAAK,CAACJ,QAAQ,CAAC;QACtCiC,QAAQ,CAACjC,QAAQ,CAAC,GAAGkC,SAAS,KAAKC,SAAS,GAAGD,SAAS,GAAGL,MAAM,CAACX,KAAK,CAACL,MAAM,CAACb,QAAQ,CAAC;MAC1F,CAAC,CAAC;MACF,OAAOpC,KAAK,CAACwE,aAAa,CAACnE,SAAS,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE6B,QAAQ,EAAE,IAAI,CAACpC,QAAQ,EAAE;QACjFkB,GAAG,EAAEgB,QAAQ,IAAI,IAAI,CAACjB;MACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO5B,qBAAqB;EAC9B,CAAC,CAACtB,KAAK,CAACK,SAAS,CAAC;EAElBJ,QAAQ,CAACqB,qBAAqB,CAAC;EAC/BA,qBAAqB,CAACd,WAAW,GAAG,eAAe,GAAGA,WAAW,GAAG,GAAG;EACvEc,qBAAqB,CAACmD,SAAS,GAAG5E,QAAQ,CAAC;IACzCsE,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG,CAAC;EACjC,CAAC,EAAEhE,KAAK,CAACuE,qBAAqB,CAACpE,gBAAgB,EAAEE,WAAW,CAAC,CAAC;EAC9DD,OAAO,CAAC4B,OAAO,CAAC,UAAUwC,MAAM,EAAE;IAChCrD,qBAAqB,CAACmC,SAAS,CAACkB,MAAM,CAAC,GAAG,SAASC,cAAcA,CAAA,EAAG;MAClE,IAAIC,WAAW;MAEf,OAAO,CAACA,WAAW,GAAG,IAAI,CAACzB,KAAK,EAAEuB,MAAM,CAAC,CAAC5C,KAAK,CAAC8C,WAAW,EAAEnD,SAAS,CAAC;IACzE,CAAC;EACH,CAAC,CAAC;EACF,IAAIoD,gBAAgB,GAAGxD,qBAAqB;EAE5C,IAAItB,KAAK,CAAC+E,UAAU,EAAE;IACpBD,gBAAgB,GAAG9E,KAAK,CAAC+E,UAAU,CAAC,UAAUvC,KAAK,EAAEW,GAAG,EAAE;MACxD,OAAOnD,KAAK,CAACwE,aAAa,CAAClD,qBAAqB,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE;QACpE2B,QAAQ,EAAEhB,GAAG;QACb6B,QAAQ,EAAE;UACRC,QAAQ,EAAElF,YAAY;UACtBmF,UAAU,EAAE;QACd,CAAC;QACDC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACFL,gBAAgB,CAACL,SAAS,GAAGnD,qBAAqB,CAACmD,SAAS;EAC9D;EAEAK,gBAAgB,CAACM,mBAAmB,GAAG/E,SAAS;EAChD;AACF;AACA;AACA;;EAEEyE,gBAAgB,CAACO,cAAc,GAAG,UAAUC,YAAY,EAAEC,SAAS,EAAEC,WAAW,EAAE;IAChF,IAAID,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBA,SAAS,GAAG,CAAC,CAAC;IAChB;IAEA,OAAOnF,cAAc,CAACkF,YAAY,EAAEzF,QAAQ,CAAC,CAAC,CAAC,EAAES,gBAAgB,EAAEiF,SAAS,CAAC,EAAEC,WAAW,CAAC;EAC7F,CAAC;EAED,OAAOV,gBAAgB;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}