{"ast": null, "code": "var createSymbol = function createSymbol(name) {\n  return \"@@redux-saga/\" + name;\n};\nvar CANCEL = /*#__PURE__*/\ncreateSymbol('CANCEL_PROMISE');\nvar CHANNEL_END_TYPE = /*#__PURE__*/\ncreateSymbol('CHANNEL_END');\nvar IO = /*#__PURE__*/\ncreateSymbol('IO');\nvar MATCH = /*#__PURE__*/\ncreateSymbol('MATCH');\nvar MULTICAST = /*#__PURE__*/\ncreateSymbol('MULTICAST');\nvar SAGA_ACTION = /*#__PURE__*/\ncreateSymbol('SAGA_ACTION');\nvar SELF_CANCELLATION = /*#__PURE__*/\ncreateSymbol('SELF_CANCELLATION');\nvar TASK = /*#__PURE__*/\ncreateSymbol('TASK');\nvar TASK_CANCEL = /*#__PURE__*/\ncreateSymbol('TASK_CANCEL');\nvar TERMINATE = /*#__PURE__*/\ncreateSymbol('TERMINATE');\nvar SAGA_LOCATION = /*#__PURE__*/\ncreateSymbol('LOCATION');\nexport { CANCEL, CHANNEL_END_TYPE, IO, MATCH, MULTICAST, SAGA_ACTION, SAGA_LOCATION, SELF_CANCELLATION, TASK, TASK_CANCEL, TERMINATE };", "map": {"version": 3, "names": ["createSymbol", "name", "CANCEL", "CHANNEL_END_TYPE", "IO", "MATCH", "MULTICAST", "SAGA_ACTION", "SELF_CANCELLATION", "TASK", "TASK_CANCEL", "TERMINATE", "SAGA_LOCATION"], "sources": ["E:/Uroom/Admin/node_modules/@redux-saga/symbols/dist/redux-saga-symbols.esm.js"], "sourcesContent": ["var createSymbol = function createSymbol(name) {\n  return \"@@redux-saga/\" + name;\n};\n\nvar CANCEL =\n/*#__PURE__*/\ncreateSymbol('CANCEL_PROMISE');\nvar CHANNEL_END_TYPE =\n/*#__PURE__*/\ncreateSymbol('CHANNEL_END');\nvar IO =\n/*#__PURE__*/\ncreateSymbol('IO');\nvar MATCH =\n/*#__PURE__*/\ncreateSymbol('MATCH');\nvar MULTICAST =\n/*#__PURE__*/\ncreateSymbol('MULTICAST');\nvar SAGA_ACTION =\n/*#__PURE__*/\ncreateSymbol('SAGA_ACTION');\nvar SELF_CANCELLATION =\n/*#__PURE__*/\ncreateSymbol('SELF_CANCELLATION');\nvar TASK =\n/*#__PURE__*/\ncreateSymbol('TASK');\nvar TASK_CANCEL =\n/*#__PURE__*/\ncreateSymbol('TASK_CANCEL');\nvar TERMINATE =\n/*#__PURE__*/\ncreateSymbol('TERMINATE');\nvar SAGA_LOCATION =\n/*#__PURE__*/\ncreateSymbol('LOCATION');\n\nexport { CANCEL, CHANNEL_END_TYPE, IO, MATCH, MULTICAST, SAGA_ACTION, SAGA_LOCATION, SELF_CANCELLATION, TASK, TASK_CANCEL, TERMINATE };\n"], "mappings": "AAAA,IAAIA,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC7C,OAAO,eAAe,GAAGA,IAAI;AAC/B,CAAC;AAED,IAAIC,MAAM,GACV;AACAF,YAAY,CAAC,gBAAgB,CAAC;AAC9B,IAAIG,gBAAgB,GACpB;AACAH,YAAY,CAAC,aAAa,CAAC;AAC3B,IAAII,EAAE,GACN;AACAJ,YAAY,CAAC,IAAI,CAAC;AAClB,IAAIK,KAAK,GACT;AACAL,YAAY,CAAC,OAAO,CAAC;AACrB,IAAIM,SAAS,GACb;AACAN,YAAY,CAAC,WAAW,CAAC;AACzB,IAAIO,WAAW,GACf;AACAP,YAAY,CAAC,aAAa,CAAC;AAC3B,IAAIQ,iBAAiB,GACrB;AACAR,YAAY,CAAC,mBAAmB,CAAC;AACjC,IAAIS,IAAI,GACR;AACAT,YAAY,CAAC,MAAM,CAAC;AACpB,IAAIU,WAAW,GACf;AACAV,YAAY,CAAC,aAAa,CAAC;AAC3B,IAAIW,SAAS,GACb;AACAX,YAAY,CAAC,WAAW,CAAC;AACzB,IAAIY,aAAa,GACjB;AACAZ,YAAY,CAAC,UAAU,CAAC;AAExB,SAASE,MAAM,EAAEC,gBAAgB,EAAEC,EAAE,EAAEC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEK,aAAa,EAAEJ,iBAAiB,EAAEC,IAAI,EAAEC,WAAW,EAAEC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}