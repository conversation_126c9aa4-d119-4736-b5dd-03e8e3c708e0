{"ast": null, "code": "\"use client\";\n\nimport React, { useCallback, useRef } from 'react';\nimport Transition from 'react-transition-group/Transition';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// Normalizes Transition callbacks when nodeRef is used.\nconst TransitionWrapper = /*#__PURE__*/React.forwardRef(({\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  onExited,\n  addEndListener,\n  children,\n  childRef,\n  ...props\n}, ref) => {\n  const nodeRef = useRef(null);\n  const mergedRef = useMergedRefs(nodeRef, childRef);\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = useCallback(normalize(onEnter), [onEnter]);\n  const handleEntering = useCallback(normalize(onEntering), [onEntering]);\n  const handleEntered = useCallback(normalize(onEntered), [onEntered]);\n  const handleExit = useCallback(normalize(onExit), [onExit]);\n  const handleExiting = useCallback(normalize(onExiting), [onExiting]);\n  const handleExited = useCallback(normalize(onExited), [onExited]);\n  const handleAddEndListener = useCallback(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return /*#__PURE__*/_jsx(Transition, {\n    ref: ref,\n    ...props,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    nodeRef: nodeRef,\n    children: typeof children === 'function' ? (status, innerProps) =>\n    // TODO: Types for RTG missing innerProps, so need to cast.\n    children(status, {\n      ...innerProps,\n      ref: attachRef\n    }) : /*#__PURE__*/React.cloneElement(children, {\n      ref: attachRef\n    })\n  });\n});\nexport default TransitionWrapper;", "map": {"version": 3, "names": ["React", "useCallback", "useRef", "Transition", "useMergedRefs", "safeFindDOMNode", "jsx", "_jsx", "TransitionWrapper", "forwardRef", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "onExited", "addEndListener", "children", "childRef", "props", "ref", "nodeRef", "mergedRef", "attachRef", "r", "normalize", "callback", "param", "current", "handleEnter", "handleEntering", "handleEntered", "handleExit", "handleExiting", "handleExited", "handleAddEndListener", "status", "innerProps", "cloneElement"], "sources": ["E:/Uroom/Admin/node_modules/react-bootstrap/esm/TransitionWrapper.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useCallback, useRef } from 'react';\nimport Transition from 'react-transition-group/Transition';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// Normalizes Transition callbacks when nodeRef is used.\nconst TransitionWrapper = /*#__PURE__*/React.forwardRef(({\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  onExited,\n  addEndListener,\n  children,\n  childRef,\n  ...props\n}, ref) => {\n  const nodeRef = useRef(null);\n  const mergedRef = useMergedRefs(nodeRef, childRef);\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = useCallback(normalize(onEnter), [onEnter]);\n  const handleEntering = useCallback(normalize(onEntering), [onEntering]);\n  const handleEntered = useCallback(normalize(onEntered), [onEntered]);\n  const handleExit = useCallback(normalize(onExit), [onExit]);\n  const handleExiting = useCallback(normalize(onExiting), [onExiting]);\n  const handleExited = useCallback(normalize(onExited), [onExited]);\n  const handleAddEndListener = useCallback(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return /*#__PURE__*/_jsx(Transition, {\n    ref: ref,\n    ...props,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    nodeRef: nodeRef,\n    children: typeof children === 'function' ? (status, innerProps) =>\n    // TODO: Types for RTG missing innerProps, so need to cast.\n    children(status, {\n      ...innerProps,\n      ref: attachRef\n    }) : /*#__PURE__*/React.cloneElement(children, {\n      ref: attachRef\n    })\n  });\n});\nexport default TransitionWrapper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,mCAAmC;AAC1D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA,MAAMC,iBAAiB,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EACvDC,OAAO;EACPC,UAAU;EACVC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,QAAQ;EACRC,cAAc;EACdC,QAAQ;EACRC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,OAAO,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMoB,SAAS,GAAGlB,aAAa,CAACiB,OAAO,EAAEH,QAAQ,CAAC;EAClD,MAAMK,SAAS,GAAGC,CAAC,IAAI;IACrBF,SAAS,CAACjB,eAAe,CAACmB,CAAC,CAAC,CAAC;EAC/B,CAAC;EACD,MAAMC,SAAS,GAAGC,QAAQ,IAAIC,KAAK,IAAI;IACrC,IAAID,QAAQ,IAAIL,OAAO,CAACO,OAAO,EAAE;MAC/BF,QAAQ,CAACL,OAAO,CAACO,OAAO,EAAED,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAME,WAAW,GAAG5B,WAAW,CAACwB,SAAS,CAACf,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAC9D,MAAMoB,cAAc,GAAG7B,WAAW,CAACwB,SAAS,CAACd,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACvE,MAAMoB,aAAa,GAAG9B,WAAW,CAACwB,SAAS,CAACb,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACpE,MAAMoB,UAAU,GAAG/B,WAAW,CAACwB,SAAS,CAACZ,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC3D,MAAMoB,aAAa,GAAGhC,WAAW,CAACwB,SAAS,CAACX,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACpE,MAAMoB,YAAY,GAAGjC,WAAW,CAACwB,SAAS,CAACV,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACjE,MAAMoB,oBAAoB,GAAGlC,WAAW,CAACwB,SAAS,CAACT,cAAc,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACrF;;EAEA,OAAO,aAAaT,IAAI,CAACJ,UAAU,EAAE;IACnCiB,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRT,OAAO,EAAEmB,WAAW;IACpBjB,SAAS,EAAEmB,aAAa;IACxBpB,UAAU,EAAEmB,cAAc;IAC1BjB,MAAM,EAAEmB,UAAU;IAClBjB,QAAQ,EAAEmB,YAAY;IACtBpB,SAAS,EAAEmB,aAAa;IACxBjB,cAAc,EAAEmB,oBAAoB;IACpCd,OAAO,EAAEA,OAAO;IAChBJ,QAAQ,EAAE,OAAOA,QAAQ,KAAK,UAAU,GAAG,CAACmB,MAAM,EAAEC,UAAU;IAC9D;IACApB,QAAQ,CAACmB,MAAM,EAAE;MACf,GAAGC,UAAU;MACbjB,GAAG,EAAEG;IACP,CAAC,CAAC,GAAG,aAAavB,KAAK,CAACsC,YAAY,CAACrB,QAAQ,EAAE;MAC7CG,GAAG,EAAEG;IACP,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,eAAef,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}