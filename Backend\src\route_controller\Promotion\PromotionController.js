const Promotion = require('../../models/Promotion');
const PromotionUser = require('../../models/PromotionUser');

// Create new promotion
exports.createPromotion = async (req, res) => {
  try {
    const promotion = new Promotion({ ...req.body, createdBy: req.user._id });
    await promotion.save();
    res.status(201).json(promotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get all promotions with pagination
exports.getAllPromotions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const status = req.query.status; // 'active', 'inactive', 'expired', 'upcoming', 'all'
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1;

    // Build filter object
    let filter = {};

    // Search filter
    if (search) {
      filter.$or = [
        { code: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter
    const now = new Date();
    if (status === 'active') {
      filter.isActive = true;
      filter.startDate = { $lte: now };
      filter.endDate = { $gte: now };
    } else if (status === 'inactive') {
      filter.isActive = false;
    } else if (status === 'expired') {
      filter.endDate = { $lt: now };
    } else if (status === 'upcoming') {
      // Upcoming: active promotions that haven't started yet
      filter.isActive = true;
      filter.startDate = { $gt: now };
    }

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalPromotions = await Promotion.countDocuments(filter);
    const totalPages = Math.ceil(totalPromotions / limit);

    // Get promotions with pagination
    const promotions = await Promotion.find(filter)
      .sort({ [sortBy]: sortOrder })
      .skip(skip)
      .limit(limit);

    // Calculate statistics
    const stats = {
      total: await Promotion.countDocuments(),
      active: await Promotion.countDocuments({
        isActive: true,
        startDate: { $lte: now },
        endDate: { $gte: now }
      }),
      inactive: await Promotion.countDocuments({ isActive: false }),
      expired: await Promotion.countDocuments({ endDate: { $lt: now } }),
      upcoming: await Promotion.countDocuments({
        isActive: true,
        startDate: { $gt: now }
      })
    };

    res.json({
      promotions,
      pagination: {
        currentPage: page,
        totalPages,
        totalPromotions,
        limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      stats,
      filters: {
        search,
        status,
        sortBy,
        sortOrder: req.query.sortOrder || 'desc'
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get promotion by ID
exports.getPromotionById = async (req, res) => {
  try {
    const promotion = await Promotion.findById(req.params.id);
    if (!promotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(promotion);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update promotion
exports.updatePromotion = async (req, res) => {
  try {
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete promotion
exports.deletePromotion = async (req, res) => {
  try {
    const deleted = await Promotion.findByIdAndDelete(req.params.id);
    if (!deleted) return res.status(404).json({ message: 'Promotion not found' });
    res.json({ message: 'Promotion deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Toggle promotion status
exports.togglePromotionStatus = async (req, res) => {
  try {
    const { isActive } = req.body;
    const updatedPromotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );
    if (!updatedPromotion) return res.status(404).json({ message: 'Promotion not found' });
    res.json(updatedPromotion);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Apply promotion code
exports.applyPromotionCode = async (req, res) => {
  try {
    const { code, orderAmount } = req.body;
    const userId = req.user?.id; // Get user ID from auth middleware

    const promotion = await Promotion.findOne({ code: code.toUpperCase(), isActive: true });

    if (!promotion) return res.status(404).json({ message: 'Invalid or inactive promotion code' });

    const now = new Date();
    if (now < promotion.startDate || now > promotion.endDate) {
      return res.status(400).json({ message: 'Promotion is not active at this time' });
    }

    if (promotion.usageLimit && promotion.usedCount >= promotion.usageLimit) {
      return res.status(400).json({ message: 'Promotion usage limit exceeded' });
    }

    if (orderAmount < promotion.minOrderAmount) {
      return res.status(400).json({ message: `Minimum order amount is ${promotion.minOrderAmount}` });
    }

    // Check if user can access this promotion
    if (promotion.type === 'PRIVATE' && userId) {
      const userPromotion = await PromotionUser.findOne({
        promotionId: promotion._id,
        userId
      });

      if (!userPromotion) {
        return res.status(403).json({ message: 'You do not have access to this promotion' });
      }
    }

    // Check user usage limit
    if (userId) {
      const userPromotion = await PromotionUser.findOne({
        promotionId: promotion._id,
        userId
      });

      const userUsedCount = userPromotion?.usedCount || 0;
      if (userUsedCount >= promotion.maxUsagePerUser) {
        return res.status(400).json({
          message: `You have reached the maximum usage limit (${promotion.maxUsagePerUser}) for this promotion`
        });
      }
    }

    let discount = 0;
    if (promotion.discountType === 'PERCENTAGE') {
      discount = (orderAmount * promotion.discountValue) / 100;
      if (promotion.maxDiscountAmount) {
        discount = Math.min(discount, promotion.maxDiscountAmount);
      }
    } else if (promotion.discountType === 'FIXED_AMOUNT') {
      discount = promotion.discountValue;
    }

    res.json({
      valid: true,
      discount,
      message: 'Promotion applied successfully',
      promotionId: promotion._id
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Lấy promotions cho user
exports.getUserPromotions = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Lấy public promotions
    const publicPromotions = await Promotion.find({
      type: 'PUBLIC',
      isActive: true,
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    });

    // Lấy private promotions của user
    const userPromotions = await PromotionUser.find({ userId })
      .populate({
        path: 'promotionId',
        match: { 
          type: 'PRIVATE',
          isActive: true,
          startDate: { $lte: new Date() },
          endDate: { $gte: new Date() }
        }
      });

    const privatePromotions = userPromotions
      .filter(up => up.promotionId)
      .map(up => ({
        ...up.promotionId.toObject(),
        userUsedCount: up.usedCount
      }));

    // Kết hợp và kiểm tra usage
    const allPromotions = [...publicPromotions, ...privatePromotions];
    
    const validPromotions = await Promise.all(
      allPromotions.map(async (promo) => {
        const userPromo = await PromotionUser.findOne({
          promotionId: promo._id,
          userId
        });
        
        const userUsedCount = userPromo?.usedCount || 0;
        const canUse = userUsedCount < promo.maxUsagePerUser;
        
        return {
          ...promo.toObject(),
          userUsedCount,
          canUse,
          remainingUses: promo.maxUsagePerUser - userUsedCount
        };
      })
    );

    res.json({
      success: true,
      promotions: validPromotions.filter(p => p.canUse)
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Assign private promotion (Admin only)
exports.assignPrivatePromotion = async (req, res) => {
  try {
    const { code, userId } = req.body;

    const promotion = await Promotion.findOne({
      code: code.toUpperCase(),
      type: 'PRIVATE',
      isActive: true
    });

    if (!promotion) {
      return res.status(404).json({ message: 'Private promotion not found' });
    }

    // Kiểm tra đã assign chưa
    const existing = await PromotionUser.findOne({
      promotionId: promotion._id,
      userId
    });

    if (existing) {
      return res.status(400).json({ message: 'Promotion already assigned to user' });
    }

    await PromotionUser.create({
      promotionId: promotion._id,
      userId,
      usedCount: 0
    });

    res.json({
      success: true,
      message: 'Private promotion assigned successfully',
      promotion
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Check and assign private promotion when user enters code
exports.checkPrivatePromotion = async (req, res) => {
  try {
    const { code } = req.body;
    const userId = req.user.id;

    const promotion = await Promotion.findOne({
      code: code.toUpperCase(),
      type: 'PRIVATE',
      isActive: true
    });

    if (!promotion) {
      return res.status(404).json({ message: 'Private promotion not found' });
    }

    // Check if already assigned
    const existing = await PromotionUser.findOne({
      promotionId: promotion._id,
      userId
    });

    if (existing) {
      return res.json({
        success: true,
        message: 'Promotion already available',
        promotion: {
          ...promotion.toObject(),
          userUsedCount: existing.usedCount,
          canUse: existing.usedCount < promotion.maxUsagePerUser
        }
      });
    }

    // Auto-assign private promotion to user
    await PromotionUser.create({
      promotionId: promotion._id,
      userId,
      usedCount: 0
    });

    res.json({
      success: true,
      message: 'Private promotion assigned and available',
      promotion: {
        ...promotion.toObject(),
        userUsedCount: 0,
        canUse: true
      }
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
