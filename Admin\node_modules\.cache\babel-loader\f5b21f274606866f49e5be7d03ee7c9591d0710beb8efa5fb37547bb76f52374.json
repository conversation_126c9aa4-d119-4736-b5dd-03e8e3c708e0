{"ast": null, "code": "import { useSSRSafeId, useIsSSR, SSRProvider } from '@react-aria/ssr';\nexport { useSSRSafeId, useIsSSR, SSRProvider };", "map": {"version": 3, "names": ["useSSRSafeId", "useIsSSR", "SSRProvider"], "sources": ["E:/Uroom/Admin/node_modules/@restart/ui/esm/ssr.js"], "sourcesContent": ["import { useSSRSafeId, useIsSSR, SSRProvider } from '@react-aria/ssr';\nexport { useSSRSafeId, useIsSSR, SSRProvider };"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,iBAAiB;AACrE,SAASF,YAAY,EAAEC,QAAQ,EAAEC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}