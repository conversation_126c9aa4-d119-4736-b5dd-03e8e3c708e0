{"ast": null, "code": "import { TASK, SAGA_ACTION, MULTICAST, IO } from '@redux-saga/symbols';\nvar undef = function undef(v) {\n  return v === null || v === undefined;\n};\nvar notUndef = function notUndef(v) {\n  return v !== null && v !== undefined;\n};\nvar func = function func(f) {\n  return typeof f === 'function';\n};\nvar number = function number(n) {\n  return typeof n === 'number';\n};\nvar string = function string(s) {\n  return typeof s === 'string';\n};\nvar array = Array.isArray;\nvar object = function object(obj) {\n  return obj && !array(obj) && typeof obj === 'object';\n};\nvar promise = function promise(p) {\n  return p && func(p.then);\n};\nvar iterator = function iterator(it) {\n  return it && func(it.next) && func(it.throw);\n};\nvar iterable = function iterable(it) {\n  return it && func(Symbol) ? func(it[Symbol.iterator]) : array(it);\n};\nvar task = function task(t) {\n  return t && t[TASK];\n};\nvar sagaAction = function sagaAction(a) {\n  return Boolean(a && a[SAGA_ACTION]);\n};\nvar observable = function observable(ob) {\n  return ob && func(ob.subscribe);\n};\nvar buffer = function buffer(buf) {\n  return buf && func(buf.isEmpty) && func(buf.take) && func(buf.put);\n};\nvar pattern = function pattern(pat) {\n  return pat && (string(pat) || symbol(pat) || func(pat) || array(pat) && pat.every(pattern));\n};\nvar channel = function channel(ch) {\n  return ch && func(ch.take) && func(ch.close);\n};\nvar stringableFunc = function stringableFunc(f) {\n  return func(f) && f.hasOwnProperty('toString');\n};\nvar symbol = function symbol(sym) {\n  return Boolean(sym) && typeof Symbol === 'function' && sym.constructor === Symbol && sym !== Symbol.prototype;\n};\nvar multicast = function multicast(ch) {\n  return channel(ch) && ch[MULTICAST];\n};\nvar effect = function effect(eff) {\n  return eff && eff[IO];\n};\nexport { array, buffer, channel, effect, func, iterable, iterator, multicast, notUndef, number, object, observable, pattern, promise, sagaAction, string, stringableFunc, symbol, task, undef };", "map": {"version": 3, "names": ["TASK", "SAGA_ACTION", "MULTICAST", "IO", "undef", "v", "undefined", "notUndef", "func", "f", "number", "n", "string", "s", "array", "Array", "isArray", "object", "obj", "promise", "p", "then", "iterator", "it", "next", "throw", "iterable", "Symbol", "task", "t", "sagaAction", "a", "Boolean", "observable", "ob", "subscribe", "buffer", "buf", "isEmpty", "take", "put", "pattern", "pat", "symbol", "every", "channel", "ch", "close", "stringableFunc", "hasOwnProperty", "sym", "constructor", "prototype", "multicast", "effect", "eff"], "sources": ["E:/Uroom/Admin/node_modules/@redux-saga/is/dist/redux-saga-is.esm.js"], "sourcesContent": ["import { TASK, SAGA_ACTION, MULTICAST, IO } from '@redux-saga/symbols';\n\nvar undef = function undef(v) {\n  return v === null || v === undefined;\n};\nvar notUndef = function notUndef(v) {\n  return v !== null && v !== undefined;\n};\nvar func = function func(f) {\n  return typeof f === 'function';\n};\nvar number = function number(n) {\n  return typeof n === 'number';\n};\nvar string = function string(s) {\n  return typeof s === 'string';\n};\nvar array = Array.isArray;\nvar object = function object(obj) {\n  return obj && !array(obj) && typeof obj === 'object';\n};\nvar promise = function promise(p) {\n  return p && func(p.then);\n};\nvar iterator = function iterator(it) {\n  return it && func(it.next) && func(it.throw);\n};\nvar iterable = function iterable(it) {\n  return it && func(Symbol) ? func(it[Symbol.iterator]) : array(it);\n};\nvar task = function task(t) {\n  return t && t[TASK];\n};\nvar sagaAction = function sagaAction(a) {\n  return Boolean(a && a[SAGA_ACTION]);\n};\nvar observable = function observable(ob) {\n  return ob && func(ob.subscribe);\n};\nvar buffer = function buffer(buf) {\n  return buf && func(buf.isEmpty) && func(buf.take) && func(buf.put);\n};\nvar pattern = function pattern(pat) {\n  return pat && (string(pat) || symbol(pat) || func(pat) || array(pat) && pat.every(pattern));\n};\nvar channel = function channel(ch) {\n  return ch && func(ch.take) && func(ch.close);\n};\nvar stringableFunc = function stringableFunc(f) {\n  return func(f) && f.hasOwnProperty('toString');\n};\nvar symbol = function symbol(sym) {\n  return Boolean(sym) && typeof Symbol === 'function' && sym.constructor === Symbol && sym !== Symbol.prototype;\n};\nvar multicast = function multicast(ch) {\n  return channel(ch) && ch[MULTICAST];\n};\nvar effect = function effect(eff) {\n  return eff && eff[IO];\n};\n\nexport { array, buffer, channel, effect, func, iterable, iterator, multicast, notUndef, number, object, observable, pattern, promise, sagaAction, string, stringableFunc, symbol, task, undef };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,EAAE,QAAQ,qBAAqB;AAEtE,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,CAAC,EAAE;EAC5B,OAAOA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS;AACtC,CAAC;AACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACF,CAAC,EAAE;EAClC,OAAOA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKC,SAAS;AACtC,CAAC;AACD,IAAIE,IAAI,GAAG,SAASA,IAAIA,CAACC,CAAC,EAAE;EAC1B,OAAO,OAAOA,CAAC,KAAK,UAAU;AAChC,CAAC;AACD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,CAAC,EAAE;EAC9B,OAAO,OAAOA,CAAC,KAAK,QAAQ;AAC9B,CAAC;AACD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,CAAC,EAAE;EAC9B,OAAO,OAAOA,CAAC,KAAK,QAAQ;AAC9B,CAAC;AACD,IAAIC,KAAK,GAAGC,KAAK,CAACC,OAAO;AACzB,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAE;EAChC,OAAOA,GAAG,IAAI,CAACJ,KAAK,CAACI,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,QAAQ;AACtD,CAAC;AACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,CAAC,EAAE;EAChC,OAAOA,CAAC,IAAIZ,IAAI,CAACY,CAAC,CAACC,IAAI,CAAC;AAC1B,CAAC;AACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,EAAE,EAAE;EACnC,OAAOA,EAAE,IAAIf,IAAI,CAACe,EAAE,CAACC,IAAI,CAAC,IAAIhB,IAAI,CAACe,EAAE,CAACE,KAAK,CAAC;AAC9C,CAAC;AACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACH,EAAE,EAAE;EACnC,OAAOA,EAAE,IAAIf,IAAI,CAACmB,MAAM,CAAC,GAAGnB,IAAI,CAACe,EAAE,CAACI,MAAM,CAACL,QAAQ,CAAC,CAAC,GAAGR,KAAK,CAACS,EAAE,CAAC;AACnE,CAAC;AACD,IAAIK,IAAI,GAAG,SAASA,IAAIA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,IAAIA,CAAC,CAAC7B,IAAI,CAAC;AACrB,CAAC;AACD,IAAI8B,UAAU,GAAG,SAASA,UAAUA,CAACC,CAAC,EAAE;EACtC,OAAOC,OAAO,CAACD,CAAC,IAAIA,CAAC,CAAC9B,WAAW,CAAC,CAAC;AACrC,CAAC;AACD,IAAIgC,UAAU,GAAG,SAASA,UAAUA,CAACC,EAAE,EAAE;EACvC,OAAOA,EAAE,IAAI1B,IAAI,CAAC0B,EAAE,CAACC,SAAS,CAAC;AACjC,CAAC;AACD,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAE;EAChC,OAAOA,GAAG,IAAI7B,IAAI,CAAC6B,GAAG,CAACC,OAAO,CAAC,IAAI9B,IAAI,CAAC6B,GAAG,CAACE,IAAI,CAAC,IAAI/B,IAAI,CAAC6B,GAAG,CAACG,GAAG,CAAC;AACpE,CAAC;AACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;EAClC,OAAOA,GAAG,KAAK9B,MAAM,CAAC8B,GAAG,CAAC,IAAIC,MAAM,CAACD,GAAG,CAAC,IAAIlC,IAAI,CAACkC,GAAG,CAAC,IAAI5B,KAAK,CAAC4B,GAAG,CAAC,IAAIA,GAAG,CAACE,KAAK,CAACH,OAAO,CAAC,CAAC;AAC7F,CAAC;AACD,IAAII,OAAO,GAAG,SAASA,OAAOA,CAACC,EAAE,EAAE;EACjC,OAAOA,EAAE,IAAItC,IAAI,CAACsC,EAAE,CAACP,IAAI,CAAC,IAAI/B,IAAI,CAACsC,EAAE,CAACC,KAAK,CAAC;AAC9C,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACvC,CAAC,EAAE;EAC9C,OAAOD,IAAI,CAACC,CAAC,CAAC,IAAIA,CAAC,CAACwC,cAAc,CAAC,UAAU,CAAC;AAChD,CAAC;AACD,IAAIN,MAAM,GAAG,SAASA,MAAMA,CAACO,GAAG,EAAE;EAChC,OAAOlB,OAAO,CAACkB,GAAG,CAAC,IAAI,OAAOvB,MAAM,KAAK,UAAU,IAAIuB,GAAG,CAACC,WAAW,KAAKxB,MAAM,IAAIuB,GAAG,KAAKvB,MAAM,CAACyB,SAAS;AAC/G,CAAC;AACD,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACP,EAAE,EAAE;EACrC,OAAOD,OAAO,CAACC,EAAE,CAAC,IAAIA,EAAE,CAAC5C,SAAS,CAAC;AACrC,CAAC;AACD,IAAIoD,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAE;EAChC,OAAOA,GAAG,IAAIA,GAAG,CAACpD,EAAE,CAAC;AACvB,CAAC;AAED,SAASW,KAAK,EAAEsB,MAAM,EAAES,OAAO,EAAES,MAAM,EAAE9C,IAAI,EAAEkB,QAAQ,EAAEJ,QAAQ,EAAE+B,SAAS,EAAE9C,QAAQ,EAAEG,MAAM,EAAEO,MAAM,EAAEgB,UAAU,EAAEQ,OAAO,EAAEtB,OAAO,EAAEW,UAAU,EAAElB,MAAM,EAAEoC,cAAc,EAAEL,MAAM,EAAEf,IAAI,EAAExB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}