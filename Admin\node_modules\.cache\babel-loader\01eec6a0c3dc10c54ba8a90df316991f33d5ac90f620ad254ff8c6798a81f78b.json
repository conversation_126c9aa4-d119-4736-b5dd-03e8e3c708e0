{"ast": null, "code": "export default function getInitialPopperStyles(position = 'absolute') {\n  return {\n    position,\n    top: '0',\n    left: '0',\n    opacity: '0',\n    pointerEvents: 'none'\n  };\n}", "map": {"version": 3, "names": ["getInitialPopperStyles", "position", "top", "left", "opacity", "pointerEvents"], "sources": ["E:/Uroom/Admin/node_modules/react-bootstrap/esm/getInitialPopperStyles.js"], "sourcesContent": ["export default function getInitialPopperStyles(position = 'absolute') {\n  return {\n    position,\n    top: '0',\n    left: '0',\n    opacity: '0',\n    pointerEvents: 'none'\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,sBAAsBA,CAACC,QAAQ,GAAG,UAAU,EAAE;EACpE,OAAO;IACLA,QAAQ;IACRC,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,GAAG;IACZC,aAAa,EAAE;EACjB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}