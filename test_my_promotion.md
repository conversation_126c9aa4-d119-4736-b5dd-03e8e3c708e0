# Test My Promotion Component

## Bước 1: Tạo Sample Promotions

### Option A: Qua Admin Panel
1. Login vào Admin panel
2. Vào Promotions section
3. Tạo promotions với data:

```json
{
  "code": "WELCOME20",
  "name": "Welcome 20% Off",
  "description": "20% discount for new customers",
  "discountType": "PERCENTAGE",
  "discountValue": 20,
  "maxDiscountAmount": 50,
  "minOrderAmount": 100,
  "startDate": "2025-01-01",
  "endDate": "2025-12-31",
  "usageLimit": 1000,
  "type": "PUBLIC",
  "maxUsagePerUser": 3,
  "isActive": true
}
```

### Option B: Qua API (nếu có admin token)
```bash
POST http://localhost:5000/api/promotions/create-samples
Headers: 
  Authorization: Bearer <admin_token>
```

## Bước 2: Test API Endpoint

### Test getUserPromotions API:
```bash
GET http://localhost:5000/api/promotions/user
Headers: 
  Authorization: Bearer <customer_token>
```

### Expected Response:
```json
{
  "success": true,
  "promotions": [
    {
      "_id": "...",
      "code": "WELCOME20",
      "name": "Welcome 20% Off",
      "description": "20% discount for new customers",
      "discountType": "PERCENTAGE",
      "discountValue": 20,
      "maxDiscountAmount": 50,
      "minOrderAmount": 100,
      "type": "PUBLIC",
      "maxUsagePerUser": 3,
      "userUsedCount": 0,
      "canUse": true,
      "remainingUses": 3
    }
  ]
}
```

## Bước 3: Debug Frontend

### Check Browser Console:
1. Mở My Account > My Promotions
2. Mở Developer Tools > Console
3. Tìm các log messages:
   - `🎯 Component: Dispatching FETCH_USER_PROMOTIONS action`
   - `🚀 Redux Saga: Fetching promotions from API...`
   - `✅ Redux Saga: API Response:`
   - `🔍 Reducer: FETCH_USER_PROMOTIONS_SUCCESS payload:`

### Check Network Tab:
1. Mở Developer Tools > Network
2. Refresh trang My Promotions
3. Tìm request đến `/api/promotions/user`
4. Check:
   - Status code (should be 200)
   - Request headers (Authorization header có đúng không)
   - Response body

## Bước 4: Debug Backend

### Check Backend Console:
Tìm các log messages từ getUserPromotions:
- `Getting promotions for user: <userId>`
- `All public promotions: <count>`
- `Public promotions in date range: <count>`
- `Total promotions after date filter: <count>`
- `Final promotions count: <count>`

## Common Issues & Solutions

### Issue 1: Empty promotions array
**Symptoms**: API trả về `{"success": true, "promotions": []}`
**Causes**: 
- Không có promotions trong database
- Date filter loại bỏ tất cả promotions
- User đã dùng hết lượt cho tất cả promotions

**Solutions**:
- Tạo sample promotions
- Check date range của promotions
- Check maxUsagePerUser settings

### Issue 2: 401 Unauthorized
**Symptoms**: API trả về 401 error
**Causes**:
- User chưa login
- Token expired
- Token không được gửi đúng cách

**Solutions**:
- Login lại
- Check localStorage có token không
- Check Authorization header

### Issue 3: Component hiển thị loading forever
**Symptoms**: Component stuck ở loading state
**Causes**:
- API call thất bại
- Redux action không được dispatch đúng
- Saga không handle error

**Solutions**:
- Check browser console for errors
- Check network tab for failed requests
- Check Redux DevTools

## Quick Debug Commands

### Check if user is logged in:
```javascript
// In browser console
localStorage.getItem('token')
```

### Check Redux state:
```javascript
// In browser console (if Redux DevTools available)
window.__REDUX_DEVTOOLS_EXTENSION__
```

### Manual API test:
```javascript
// In browser console
fetch('/api/promotions/user', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
})
.then(r => r.json())
.then(console.log)
```
