const mongoose = require('mongoose');

const promotionUserSchema = new mongoose.Schema({
  promotionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Promotion',
    required: true,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  usedCount: {
    type: Number,
    default: 0,
  },
  assignedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  versionKey: false,
  timestamps: true
});

// Compound index để tránh duplicate
promotionUserSchema.index({ promotionId: 1, userId: 1 }, { unique: true });

module.exports = mongoose.model('PromotionUser', promotionUserSchema);