{"ast": null, "code": "/**\n * Returns the owner document of a given element.\n * \n * @param node the element\n */\nexport default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}", "map": {"version": 3, "names": ["ownerDocument", "node", "document"], "sources": ["E:/Uroom/Admin/node_modules/dom-helpers/esm/ownerDocument.js"], "sourcesContent": ["/**\n * Returns the owner document of a given element.\n * \n * @param node the element\n */\nexport default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,aAAaA,CAACC,IAAI,EAAE;EAC1C,OAAOA,IAAI,IAAIA,IAAI,CAACD,aAAa,IAAIE,QAAQ;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}