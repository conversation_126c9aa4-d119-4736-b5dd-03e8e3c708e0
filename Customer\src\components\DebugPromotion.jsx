import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Alert } from 'react-bootstrap';
import Factories from '../redux/promotion/factories';

const DebugPromotion = () => {
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🧪 Debug: Testing API call...');
      const response = await Factories.fetchUserPromotions();
      console.log('🧪 Debug: API Response:', response);
      
      setResult({
        status: response.status,
        data: response.data,
        headers: response.headers
      });
    } catch (err) {
      console.error('🧪 Debug: API Error:', err);
      setError({
        message: err.message,
        status: err.response?.status,
        data: err.response?.data
      });
    } finally {
      setLoading(false);
    }
  };

  const testToken = () => {
    const token = localStorage.getItem('token');
    console.log('🧪 Debug: Token check:', {
      exists: !!token,
      preview: token ? `${token.substring(0, 20)}...` : 'null',
      length: token?.length
    });
    
    setResult({
      token: {
        exists: !!token,
        preview: token ? `${token.substring(0, 20)}...` : 'null',
        length: token?.length
      }
    });
  };

  return (
    <Card className="m-3">
      <Card.Header>
        <h5>🧪 Promotion API Debug Tool</h5>
      </Card.Header>
      <Card.Body>
        <div className="d-flex gap-2 mb-3">
          <Button 
            variant="primary" 
            onClick={testAPI}
            disabled={loading}
          >
            {loading ? 'Testing...' : 'Test API Call'}
          </Button>
          <Button 
            variant="secondary" 
            onClick={testToken}
          >
            Check Token
          </Button>
        </div>

        {error && (
          <Alert variant="danger">
            <h6>❌ Error:</h6>
            <pre>{JSON.stringify(error, null, 2)}</pre>
          </Alert>
        )}

        {result && (
          <Alert variant="success">
            <h6>✅ Result:</h6>
            <pre>{JSON.stringify(result, null, 2)}</pre>
          </Alert>
        )}

        <Alert variant="info">
          <h6>📝 Instructions:</h6>
          <ol>
            <li>Make sure you're logged in as a customer</li>
            <li>Click "Check Token" to verify authentication</li>
            <li>Click "Test API Call" to test the promotions endpoint</li>
            <li>Check browser console for detailed logs</li>
          </ol>
        </Alert>
      </Card.Body>
    </Card>
  );
};

export default DebugPromotion;
