{"ast": null, "code": "'use strict';\n\n/** @type {import('./round')} */\nmodule.exports = Math.round;", "map": {"version": 3, "names": ["module", "exports", "Math", "round"], "sources": ["E:/Uroom/Admin/node_modules/math-intrinsics/round.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./round')} */\nmodule.exports = Math.round;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,IAAI,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}