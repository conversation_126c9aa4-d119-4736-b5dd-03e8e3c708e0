{"ast": null, "code": "var _jsxFileName = \"E:\\\\Uroom\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\components\\\\PromotionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { <PERSON>dal, <PERSON><PERSON>, Card, Badge, Spinner, Form, InputGroup } from \"react-bootstrap\";\nimport { FaTag, FaTimes, FaCheck, FaPlus } from \"react-icons/fa\";\nimport Utils from \"../../../../utils/Utils\";\nimport Factories from \"../../../../redux/promotion/factories\";\nimport \"../../../../css/PromotionModal.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PromotionModal = ({\n  show,\n  onHide,\n  totalPrice,\n  onApplyPromotion,\n  currentPromotionId\n}) => {\n  _s();\n  const [promotions, setPromotions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\n  const [applying, setApplying] = useState(false);\n  const [promotionCode, setPromotionCode] = useState(\"\");\n  const [checkingCode, setCheckingCode] = useState(false);\n  const API_BASE_URL = getApiUrl(); // Add this line\n\n  useEffect(() => {\n    if (show && totalPrice > 0) {\n      fetchPromotions();\n    }\n  }, [show, totalPrice]);\n  const fetchPromotions = async () => {\n    setLoading(true);\n    try {\n      let promotionList = [];\n      try {\n        const token = localStorage.getItem('token');\n        console.log(\"Fetching promotions from API...\");\n        console.log(\"API URL:\", `${API_BASE_URL}/api/promotions/user`);\n        console.log(\"Token exists:\", !!token);\n        console.log(\"Token preview:\", token ? `${token.substring(0, 20)}...` : 'null');\n\n        // Replace hardcoded URL with environment-based URL\n        const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        console.log(\"API Response:\", response.data);\n        promotionList = response.data.promotions || response.data.data || response.data || [];\n        console.log(\"Promotion list from API:\", promotionList);\n        console.log(\"API response structure:\", response.data);\n        if (!Array.isArray(promotionList)) {\n          console.log(\"API returned invalid data format, using mock data\");\n          throw new Error(\"Invalid data format from API\");\n        }\n\n        // Don't fallback to mock data if API returns empty array - that's valid\n        console.log(`API returned ${promotionList.length} promotions`);\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4, _apiError$response5;\n        console.error(\"API Error details:\", {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers\n        });\n\n        // Check if it's an authentication error\n        if (((_apiError$response5 = apiError.response) === null || _apiError$response5 === void 0 ? void 0 : _apiError$response5.status) === 401) {\n          console.log(\"Authentication error - user may not be logged in\");\n          setPromotions([]);\n          setLoading(false);\n          return;\n        }\n\n        // For other errors, use mock data temporarily\n        console.log(\"Using mock promotion data due to API error\");\n        promotionList = [{\n          _id: \"1\",\n          code: \"SAVE20\",\n          description: \"Save $20 on orders over $100\",\n          discountType: \"fixed\",\n          discountValue: 20,\n          minOrderAmount: 100,\n          maxDiscount: 20,\n          expiryDate: \"2025-12-31\",\n          isActive: true\n        }, {\n          _id: \"2\",\n          code: \"PERCENT10\",\n          description: \"10% off on all bookings\",\n          discountType: \"percentage\",\n          discountValue: 10,\n          minOrderAmount: 50,\n          maxDiscount: 50,\n          expiryDate: \"2025-12-31\",\n          isActive: true\n        }, {\n          _id: \"3\",\n          code: \"NEWUSER50\",\n          description: \"Special discount for new users\",\n          discountType: \"fixed\",\n          discountValue: 50,\n          minOrderAmount: 200,\n          maxDiscount: 50,\n          expiryDate: \"2025-06-30\",\n          isActive: true\n        }, {\n          _id: \"4\",\n          code: \"EXPIRED\",\n          description: \"This promotion has expired\",\n          discountType: \"fixed\",\n          discountValue: 30,\n          minOrderAmount: 80,\n          maxDiscount: 30,\n          expiryDate: \"2024-12-31\",\n          isActive: false\n        }];\n      }\n      console.log(\"Total price for validation:\", totalPrice);\n      console.log(\"Processing\", promotionList.length, \"promotions\");\n\n      // Backend already provides validated promotions with canUse flag\n      const validatedPromotions = promotionList.map(promo => {\n        // Calculate discount for display\n        let discount = 0;\n        if (promo.discountType === 'PERCENTAGE') {\n          discount = totalPrice * promo.discountValue / 100;\n          if (promo.maxDiscountAmount) {\n            discount = Math.min(discount, promo.maxDiscountAmount);\n          }\n        } else if (promo.discountType === 'FIXED_AMOUNT') {\n          discount = promo.discountValue;\n        }\n        const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\n        const isValid = promo.canUse && meetsMinOrder;\n        let message = \"\";\n        if (isValid) {\n          message = `Save ${Utils.formatCurrency(discount)}`;\n        } else if (!meetsMinOrder) {\n          message = `Minimum order ${Utils.formatCurrency(promo.minOrderAmount)} required`;\n        } else {\n          message = \"Not available\";\n        }\n        return {\n          ...promo,\n          isValid,\n          discount,\n          message\n        };\n      });\n      console.log(\"Final validated promotions:\", validatedPromotions);\n\n      // Chỉ hiển thị promotion có thể dùng được hoặc sắp có thể dùng (chưa bắt đầu)\n      // Ẩn những promotion đã hết hạn, không đủ điều kiện, hoặc không active\n      const displayPromotions = validatedPromotions.filter(promo => {\n        const now = new Date();\n        const startDate = new Date(promo.startDate || promo.expiryDate || '2025-01-01');\n        const endDate = new Date(promo.endDate || promo.expiryDate || '2025-12-31');\n\n        // Chỉ hiển thị nếu: promotion chưa hết hạn và đang active\n        const notExpired = now <= endDate;\n        const isActive = promo.isActive !== false;\n        return notExpired && isActive;\n      });\n      console.log(\"Display promotions:\", displayPromotions.length, \"of\", validatedPromotions.length);\n      console.log(\"Available now:\", displayPromotions.filter(p => p.isValid).length);\n      console.log(\"Starting soon:\", displayPromotions.filter(p => {\n        var _p$message;\n        return !p.isValid && ((_p$message = p.message) === null || _p$message === void 0 ? void 0 : _p$message.includes(\"not started\"));\n      }).length);\n\n      // Sắp xếp promotions: Available trước, starting soon sau, và theo discount giảm dần\n      const sortedPromotions = displayPromotions.sort((a, b) => {\n        // Available promotions lên trước\n        if (a.isValid && !b.isValid) return -1;\n        if (!a.isValid && b.isValid) return 1;\n\n        // Trong cùng loại, sắp xếp theo discount giảm dần\n        return b.discount - a.discount;\n      });\n      setPromotions(sortedPromotions);\n    } catch (error) {\n      console.error(\"Error fetching promotions:\", error);\n      setPromotions([]);\n    }\n    setLoading(false);\n  };\n  const handleApplyPromotion = async promotion => {\n    if (!promotion.isValid) return;\n    setApplying(true);\n    try {\n      try {\n        // Replace hardcoded URL with environment-based URL\n        const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n          code: promotion.code,\n          orderAmount: totalPrice\n        });\n        if (response.data.valid) {\n          onApplyPromotion({\n            code: promotion.code,\n            discount: response.data.discount,\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\n            promotionId: response.data.promotionId\n          });\n          onHide();\n        }\n      } catch (apiError) {\n        // Mock logic remains the same\n        console.log(\"Using mock promotion application\");\n        onApplyPromotion({\n          code: promotion.code,\n          discount: promotion.discount,\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\n          promotionId: promotion._id\n        });\n        onHide();\n      }\n    } catch (error) {\n      console.error(\"Error applying promotion:\", error);\n    }\n    setApplying(false);\n  };\n  const handleRemovePromotion = () => {\n    onApplyPromotion({\n      code: \"\",\n      discount: 0,\n      message: \"\",\n      promotionId: null\n    });\n    onHide();\n  };\n  const handleCheckPromotionCode = async () => {\n    if (!promotionCode.trim()) return;\n    setCheckingCode(true);\n    try {\n      // First check if it's a private promotion\n      const checkResponse = await axios.post(`${API_BASE_URL}/api/promotions/check-private`, {\n        code: promotionCode.trim()\n      }, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (checkResponse.data.success) {\n        // Refresh promotions list to show the newly assigned promotion\n        await fetchPromotions();\n        setPromotionCode(\"\");\n        alert(\"Private promotion added to your account!\");\n      }\n    } catch (error) {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 404) {\n        // Try to apply as regular promotion\n        try {\n          const applyResponse = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\n            code: promotionCode.trim(),\n            orderAmount: totalPrice\n          }, {\n            headers: {\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            }\n          });\n          if (applyResponse.data.valid) {\n            onApplyPromotion({\n              code: promotionCode.trim(),\n              discount: applyResponse.data.discount,\n              message: `Promotion applied: -${Utils.formatCurrency(applyResponse.data.discount)}`,\n              promotionId: applyResponse.data.promotionId\n            });\n            onHide();\n          }\n        } catch (applyError) {\n          var _applyError$response, _applyError$response$;\n          alert(((_applyError$response = applyError.response) === null || _applyError$response === void 0 ? void 0 : (_applyError$response$ = _applyError$response.data) === null || _applyError$response$ === void 0 ? void 0 : _applyError$response$.message) || \"Invalid promotion code\");\n        }\n      } else {\n        var _error$response2, _error$response2$data;\n        alert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Error checking promotion code\");\n      }\n    }\n    setCheckingCode(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\",\n        color: \"white\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaTag, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), \"Select Promotion\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        color: \"white\",\n        maxHeight: \"60vh\",\n        overflowY: \"auto\"\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          variant: \"light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2\",\n          children: \"Loading promotions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [currentPromotionId && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Current Applied Promotion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"promotion-card current-promotion\",\n            style: {\n              backgroundColor: \"rgba(40, 167, 69, 0.2)\",\n              borderColor: \"#28a745\",\n              border: \"2px solid #28a745\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"py-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"text-success me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success fw-bold\",\n                    children: \"Applied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-danger\",\n                  size: \"sm\",\n                  onClick: handleRemovePromotion,\n                  disabled: applying,\n                  children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 25\n                  }, this), \"Remove\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-3\",\n            children: \"Enter Promotion Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"text\",\n              placeholder: \"Enter promotion code...\",\n              value: promotionCode,\n              onChange: e => setPromotionCode(e.target.value.toUpperCase()),\n              style: {\n                backgroundColor: \"rgba(255,255,255,0.1)\",\n                borderColor: \"rgba(255,255,255,0.3)\",\n                color: \"white\"\n              },\n              onKeyDown: e => {\n                if (e.key === 'Enter') {\n                  handleCheckPromotionCode();\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-light\",\n              onClick: handleCheckPromotionCode,\n              disabled: checkingCode || !promotionCode.trim(),\n              children: checkingCode ? /*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 23\n                }, this), \"Add\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted mt-1 d-block\",\n            children: \"Enter a promotion code to add it to your account or apply it directly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-3\",\n          children: [\"Available Promotions\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"small ms-2\",\n            style: {\n              color: 'rgba(255,255,255,0.6)'\n            },\n            children: [\"(\", promotions.filter(p => p.isValid).length, \" ready, \", promotions.filter(p => !p.isValid).length, \" starting soon)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this), promotions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          style: {\n            color: 'rgba(255,255,255,0.7)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n            size: 48,\n            className: \"mb-3\",\n            style: {\n              opacity: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No promotions available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [promotions.filter(p => p.isValid).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row g-3 mb-4\",\n            children: promotions.filter(p => p.isValid).map(promotion => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-12\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: `promotion-card ${currentPromotionId === promotion._id ? 'current' : ''}`,\n                style: {\n                  backgroundColor: currentPromotionId === promotion._id ? \"rgba(40, 167, 69, 0.2)\" : \"rgba(255,255,255,0.1)\",\n                  borderColor: currentPromotionId === promotion._id ? \"#28a745\" : \"rgba(255,255,255,0.3)\",\n                  cursor: \"pointer\",\n                  transition: \"all 0.3s ease\"\n                },\n                onClick: () => handleApplyPromotion(promotion),\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"py-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between align-items-start\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-grow-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                          className: \"me-2 text-primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 432,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0 fw-bold\",\n                          children: promotion.code\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 433,\n                          columnNumber: 35\n                        }, this), currentPromotionId === promotion._id && /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          className: \"ms-2\",\n                          children: \"Applied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 435,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          className: \"ms-2\",\n                          children: \"Available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 437,\n                          columnNumber: 35\n                        }, this), promotion.type === 'PRIVATE' && /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"warning\",\n                          className: \"ms-2\",\n                          children: \"Private\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 439,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 431,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"mb-2 small\",\n                        style: {\n                          color: 'rgba(255,255,255,0.7)'\n                        },\n                        children: promotion.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 443,\n                        columnNumber: 33\n                      }, this), promotion.remainingUses !== undefined && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-info\",\n                          children: [\"Remaining uses: \", promotion.remainingUses, \"/\", promotion.maxUsagePerUser]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 448,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 447,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-between align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-success fw-bold\",\n                            children: [\"Save \", Utils.formatCurrency(promotion.discount)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 456,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 455,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-end\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"small\",\n                            children: [promotion.minOrderAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-success\",\n                              children: [\"Min: \", Utils.formatCurrency(promotion.minOrderAmount), \" \\u2713\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 464,\n                              columnNumber: 41\n                            }, this), promotion.maxDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                color: 'rgba(255,255,255,0.6)'\n                              },\n                              children: [\"Max: \", Utils.formatCurrency(promotion.maxDiscount)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 469,\n                              columnNumber: 41\n                            }, this), promotion.expiryDate && /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-success\",\n                              children: [\"Expires: \", new Date(promotion.expiryDate).toLocaleDateString(), \" \\u2713\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 474,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 462,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 461,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 454,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 430,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 25\n              }, this)\n            }, promotion._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 19\n          }, this), promotions.filter(p => !p.isValid).length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"mb-3 text-warning\",\n              children: [\"Starting Soon (\", promotions.filter(p => !p.isValid).length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row g-3\",\n              children: promotions.filter(p => !p.isValid).map(promotion => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12\",\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"promotion-card disabled\",\n                  style: {\n                    backgroundColor: \"rgba(255, 193, 7, 0.1)\",\n                    borderColor: \"rgba(255, 193, 7, 0.5)\",\n                    cursor: \"not-allowed\",\n                    opacity: 0.8,\n                    transition: \"all 0.3s ease\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"py-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex justify-content-between align-items-start\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-grow-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(FaTag, {\n                            className: \"me-2 text-warning\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 513,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                            className: \"mb-0 fw-bold\",\n                            children: promotion.code\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 514,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                            bg: \"warning\",\n                            className: \"ms-2\",\n                            style: {\n                              color: 'white'\n                            },\n                            children: \"Starting Soon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 515,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 512,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"mb-2 small\",\n                          style: {\n                            color: 'rgba(255,255,255,0.7)'\n                          },\n                          children: promotion.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 518,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex justify-content-between align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-warning small fw-bold\",\n                              children: promotion.message\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 522,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 521,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-end\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"small\",\n                              children: [promotion.minOrderAmount && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `${totalPrice >= promotion.minOrderAmount ? 'text-success' : 'text-warning'}`,\n                                children: [\"Min: \", Utils.formatCurrency(promotion.minOrderAmount), totalPrice >= promotion.minOrderAmount ? ' ✓' : ' ✗']\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 530,\n                                columnNumber: 43\n                              }, this), promotion.maxDiscount && /*#__PURE__*/_jsxDEV(\"div\", {\n                                style: {\n                                  color: 'rgba(255,255,255,0.6)'\n                                },\n                                children: [\"Max: \", Utils.formatCurrency(promotion.maxDiscount)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 536,\n                                columnNumber: 43\n                              }, this), (promotion.startDate || promotion.expiryDate) && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"text-warning\",\n                                children: [\"Starts: \", new Date(promotion.startDate || promotion.expiryDate).toLocaleDateString()]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 541,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 528,\n                              columnNumber: 39\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 527,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 520,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 27\n                }, this)\n              }, promotion._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      style: {\n        backgroundColor: \"rgba(20, 30, 70, 0.95)\",\n        borderColor: \"rgba(255,255,255,0.2)\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline-light\",\n        onClick: onHide,\n        disabled: applying,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 296,\n    columnNumber: 5\n  }, this);\n};\n_s(PromotionModal, \"4aWHLDgIuAZPWkATBXonslzcXws=\");\n_c = PromotionModal;\nexport default PromotionModal;\nvar _c;\n$RefreshReg$(_c, \"PromotionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Modal", "<PERSON><PERSON>", "Card", "Badge", "Spinner", "Form", "InputGroup", "FaTag", "FaTimes", "FaCheck", "FaPlus", "Utils", "Factories", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PromotionModal", "show", "onHide", "totalPrice", "onApplyPromotion", "currentPromotionId", "_s", "promotions", "setPromotions", "loading", "setLoading", "selectedPromotion", "setSelectedPromotion", "applying", "setApplying", "promotionCode", "setPromotionCode", "checkingCode", "setCheckingCode", "API_BASE_URL", "getApiUrl", "fetchPromotions", "promotionList", "token", "localStorage", "getItem", "console", "log", "substring", "response", "axios", "get", "headers", "data", "Array", "isArray", "Error", "length", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "_apiError$response5", "error", "message", "status", "statusText", "_id", "code", "description", "discountType", "discountValue", "minOrderAmount", "maxDiscount", "expiryDate", "isActive", "validatedPromotions", "map", "promo", "discount", "maxDiscountAmount", "Math", "min", "meetsMinOrder", "<PERSON><PERSON><PERSON><PERSON>", "canUse", "formatCurrency", "displayPromotions", "filter", "now", "Date", "startDate", "endDate", "notExpired", "p", "_p$message", "includes", "sortedPromotions", "sort", "a", "b", "handleApplyPromotion", "promotion", "post", "orderAmount", "valid", "promotionId", "handleRemovePromotion", "handleCheckPromotionCode", "trim", "checkResponse", "success", "alert", "_error$response", "applyResponse", "applyError", "_applyError$response", "_applyError$response$", "_error$response2", "_error$response2$data", "size", "centered", "children", "Header", "closeButton", "style", "backgroundColor", "borderColor", "color", "Title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "maxHeight", "overflowY", "animation", "variant", "border", "onClick", "disabled", "Control", "type", "placeholder", "value", "onChange", "e", "target", "toUpperCase", "onKeyDown", "key", "opacity", "cursor", "transition", "bg", "remainingUses", "undefined", "maxUsagePerUser", "toLocaleDateString", "Footer", "_c", "$RefreshReg$"], "sources": ["E:/Uroom/Customer/src/pages/customer/home/<USER>/PromotionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Spinner, Form, InputGroup } from \"react-bootstrap\";\r\nimport { FaTag, FaTimes, FaCheck, FaPlus } from \"react-icons/fa\";\r\nimport Utils from \"../../../../utils/Utils\";\r\nimport Factories from \"../../../../redux/promotion/factories\";\r\nimport \"../../../../css/PromotionModal.css\";\r\n\r\nconst PromotionModal = ({ show, onHide, totalPrice, onApplyPromotion, currentPromotionId }) => {\r\n  const [promotions, setPromotions] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [selectedPromotion, setSelectedPromotion] = useState(null);\r\n  const [applying, setApplying] = useState(false);\r\n  const [promotionCode, setPromotionCode] = useState(\"\");\r\n  const [checkingCode, setCheckingCode] = useState(false);\r\n\r\n  const API_BASE_URL = getApiUrl(); // Add this line\r\n\r\n  useEffect(() => {\r\n    if (show && totalPrice > 0) {\r\n      fetchPromotions();\r\n    }\r\n  }, [show, totalPrice]);\r\n\r\n  const fetchPromotions = async () => {\r\n    setLoading(true);\r\n    try {\r\n      let promotionList = [];\r\n      try {\r\n        const token = localStorage.getItem('token');\r\n        console.log(\"Fetching promotions from API...\");\r\n        console.log(\"API URL:\", `${API_BASE_URL}/api/promotions/user`);\r\n        console.log(\"Token exists:\", !!token);\r\n        console.log(\"Token preview:\", token ? `${token.substring(0, 20)}...` : 'null');\r\n\r\n        // Replace hardcoded URL with environment-based URL\r\n        const response = await axios.get(`${API_BASE_URL}/api/promotions/user`, {\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`\r\n          }\r\n        });\r\n        console.log(\"API Response:\", response.data);\r\n        \r\n        promotionList = response.data.promotions || response.data.data || response.data || [];\r\n        console.log(\"Promotion list from API:\", promotionList);\r\n        console.log(\"API response structure:\", response.data);\r\n\r\n        if (!Array.isArray(promotionList)) {\r\n          console.log(\"API returned invalid data format, using mock data\");\r\n          throw new Error(\"Invalid data format from API\");\r\n        }\r\n\r\n        // Don't fallback to mock data if API returns empty array - that's valid\r\n        console.log(`API returned ${promotionList.length} promotions`);\r\n      } catch (apiError) {\r\n        console.error(\"API Error details:\", {\r\n          message: apiError.message,\r\n          status: apiError.response?.status,\r\n          statusText: apiError.response?.statusText,\r\n          data: apiError.response?.data,\r\n          headers: apiError.response?.headers\r\n        });\r\n\r\n        // Check if it's an authentication error\r\n        if (apiError.response?.status === 401) {\r\n          console.log(\"Authentication error - user may not be logged in\");\r\n          setPromotions([]);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // For other errors, use mock data temporarily\r\n        console.log(\"Using mock promotion data due to API error\");\r\n        promotionList = [\r\n          {\r\n            _id: \"1\",\r\n            code: \"SAVE20\",\r\n            description: \"Save $20 on orders over $100\",\r\n            discountType: \"fixed\",\r\n            discountValue: 20,\r\n            minOrderAmount: 100,\r\n            maxDiscount: 20,\r\n            expiryDate: \"2025-12-31\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"2\", \r\n            code: \"PERCENT10\",\r\n            description: \"10% off on all bookings\",\r\n            discountType: \"percentage\",\r\n            discountValue: 10,\r\n            minOrderAmount: 50,\r\n            maxDiscount: 50,\r\n            expiryDate: \"2025-12-31\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"3\",\r\n            code: \"NEWUSER50\",\r\n            description: \"Special discount for new users\",\r\n            discountType: \"fixed\", \r\n            discountValue: 50,\r\n            minOrderAmount: 200,\r\n            maxDiscount: 50,\r\n            expiryDate: \"2025-06-30\",\r\n            isActive: true\r\n          },\r\n          {\r\n            _id: \"4\",\r\n            code: \"EXPIRED\",\r\n            description: \"This promotion has expired\",\r\n            discountType: \"fixed\",\r\n            discountValue: 30,\r\n            minOrderAmount: 80,\r\n            maxDiscount: 30,\r\n            expiryDate: \"2024-12-31\",\r\n            isActive: false\r\n          }\r\n        ];\r\n      }\r\n      \r\n      console.log(\"Total price for validation:\", totalPrice);\r\n      console.log(\"Processing\", promotionList.length, \"promotions\");\r\n\r\n      // Backend already provides validated promotions with canUse flag\r\n      const validatedPromotions = promotionList.map((promo) => {\r\n        // Calculate discount for display\r\n        let discount = 0;\r\n        if (promo.discountType === 'PERCENTAGE') {\r\n          discount = (totalPrice * promo.discountValue) / 100;\r\n          if (promo.maxDiscountAmount) {\r\n            discount = Math.min(discount, promo.maxDiscountAmount);\r\n          }\r\n        } else if (promo.discountType === 'FIXED_AMOUNT') {\r\n          discount = promo.discountValue;\r\n        }\r\n\r\n        const meetsMinOrder = totalPrice >= (promo.minOrderAmount || 0);\r\n        const isValid = promo.canUse && meetsMinOrder;\r\n\r\n        let message = \"\";\r\n        if (isValid) {\r\n          message = `Save ${Utils.formatCurrency(discount)}`;\r\n        } else if (!meetsMinOrder) {\r\n          message = `Minimum order ${Utils.formatCurrency(promo.minOrderAmount)} required`;\r\n        } else {\r\n          message = \"Not available\";\r\n        }\r\n\r\n        return {\r\n          ...promo,\r\n          isValid,\r\n          discount,\r\n          message,\r\n        };\r\n      });\r\n      \r\n      console.log(\"Final validated promotions:\", validatedPromotions);\r\n      \r\n      // Chỉ hiển thị promotion có thể dùng được hoặc sắp có thể dùng (chưa bắt đầu)\r\n      // Ẩn những promotion đã hết hạn, không đủ điều kiện, hoặc không active\r\n      const displayPromotions = validatedPromotions.filter(promo => {\r\n        const now = new Date();\r\n        const startDate = new Date(promo.startDate || promo.expiryDate || '2025-01-01');\r\n        const endDate = new Date(promo.endDate || promo.expiryDate || '2025-12-31');\r\n        \r\n        // Chỉ hiển thị nếu: promotion chưa hết hạn và đang active\r\n        const notExpired = now <= endDate;\r\n        const isActive = promo.isActive !== false;\r\n        \r\n        return notExpired && isActive;\r\n      });\r\n      \r\n      console.log(\"Display promotions:\", displayPromotions.length, \"of\", validatedPromotions.length);\r\n      console.log(\"Available now:\", displayPromotions.filter(p => p.isValid).length);\r\n      console.log(\"Starting soon:\", displayPromotions.filter(p => !p.isValid && p.message?.includes(\"not started\")).length);\r\n      \r\n      // Sắp xếp promotions: Available trước, starting soon sau, và theo discount giảm dần\r\n      const sortedPromotions = displayPromotions.sort((a, b) => {\r\n        // Available promotions lên trước\r\n        if (a.isValid && !b.isValid) return -1;\r\n        if (!a.isValid && b.isValid) return 1;\r\n        \r\n        // Trong cùng loại, sắp xếp theo discount giảm dần\r\n        return b.discount - a.discount;\r\n      });\r\n      \r\n      setPromotions(sortedPromotions);\r\n    } catch (error) {\r\n      console.error(\"Error fetching promotions:\", error);\r\n      setPromotions([]);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  const handleApplyPromotion = async (promotion) => {\r\n    if (!promotion.isValid) return;\r\n    \r\n    setApplying(true);\r\n    try {\r\n      try {\r\n        // Replace hardcoded URL with environment-based URL\r\n        const response = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n          code: promotion.code,\r\n          orderAmount: totalPrice,\r\n        });\r\n        \r\n        if (response.data.valid) {\r\n          onApplyPromotion({\r\n            code: promotion.code,\r\n            discount: response.data.discount,\r\n            message: `Promotion applied: -${Utils.formatCurrency(response.data.discount)}`,\r\n            promotionId: response.data.promotionId,\r\n          });\r\n          onHide();\r\n        }\r\n      } catch (apiError) {\r\n        // Mock logic remains the same\r\n        console.log(\"Using mock promotion application\");\r\n        onApplyPromotion({\r\n          code: promotion.code,\r\n          discount: promotion.discount,\r\n          message: `Promotion applied: -${Utils.formatCurrency(promotion.discount)}`,\r\n          promotionId: promotion._id,\r\n        });\r\n        onHide();\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error applying promotion:\", error);\r\n    }\r\n    setApplying(false);\r\n  };\r\n\r\n  const handleRemovePromotion = () => {\r\n    onApplyPromotion({\r\n      code: \"\",\r\n      discount: 0,\r\n      message: \"\",\r\n      promotionId: null,\r\n    });\r\n    onHide();\r\n  };\r\n\r\n  const handleCheckPromotionCode = async () => {\r\n    if (!promotionCode.trim()) return;\r\n\r\n    setCheckingCode(true);\r\n    try {\r\n      // First check if it's a private promotion\r\n      const checkResponse = await axios.post(`${API_BASE_URL}/api/promotions/check-private`, {\r\n        code: promotionCode.trim()\r\n      }, {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      });\r\n\r\n      if (checkResponse.data.success) {\r\n        // Refresh promotions list to show the newly assigned promotion\r\n        await fetchPromotions();\r\n        setPromotionCode(\"\");\r\n        alert(\"Private promotion added to your account!\");\r\n      }\r\n    } catch (error) {\r\n      if (error.response?.status === 404) {\r\n        // Try to apply as regular promotion\r\n        try {\r\n          const applyResponse = await axios.post(`${API_BASE_URL}/api/promotions/apply`, {\r\n            code: promotionCode.trim(),\r\n            orderAmount: totalPrice,\r\n          }, {\r\n            headers: {\r\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n            }\r\n          });\r\n\r\n          if (applyResponse.data.valid) {\r\n            onApplyPromotion({\r\n              code: promotionCode.trim(),\r\n              discount: applyResponse.data.discount,\r\n              message: `Promotion applied: -${Utils.formatCurrency(applyResponse.data.discount)}`,\r\n              promotionId: applyResponse.data.promotionId,\r\n            });\r\n            onHide();\r\n          }\r\n        } catch (applyError) {\r\n          alert(applyError.response?.data?.message || \"Invalid promotion code\");\r\n        }\r\n      } else {\r\n        alert(error.response?.data?.message || \"Error checking promotion code\");\r\n      }\r\n    }\r\n    setCheckingCode(false);\r\n  };\r\n\r\n  return (\r\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\r\n      <Modal.Header \r\n        closeButton \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\",\r\n          color: \"white\"\r\n        }}\r\n      >\r\n        <Modal.Title className=\"d-flex align-items-center\">\r\n          <FaTag className=\"me-2\" />\r\n          Select Promotion\r\n        </Modal.Title>\r\n      </Modal.Header>\r\n      \r\n      <Modal.Body \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          color: \"white\",\r\n          maxHeight: \"60vh\",\r\n          overflowY: \"auto\"\r\n        }}\r\n      >\r\n        {loading ? (\r\n          <div className=\"text-center py-4\">\r\n            <Spinner animation=\"border\" variant=\"light\" />\r\n            <div className=\"mt-2\">Loading promotions...</div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Current promotion section */}\r\n            {currentPromotionId && (\r\n              <div className=\"mb-4\">\r\n                <h6 className=\"mb-3\">Current Applied Promotion</h6>\r\n                <Card \r\n                  className=\"promotion-card current-promotion\"\r\n                  style={{ \r\n                    backgroundColor: \"rgba(40, 167, 69, 0.2)\", \r\n                    borderColor: \"#28a745\",\r\n                    border: \"2px solid #28a745\"\r\n                  }}\r\n                >\r\n                  <Card.Body className=\"py-3\">\r\n                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <FaCheck className=\"text-success me-2\" />\r\n                        <span className=\"text-success fw-bold\">Applied</span>\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={handleRemovePromotion}\r\n                        disabled={applying}\r\n                      >\r\n                        <FaTimes className=\"me-1\" />\r\n                        Remove\r\n                      </Button>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n            )}\r\n\r\n            {/* Promotion Code Input */}\r\n            <div className=\"mb-4\">\r\n              <h6 className=\"mb-3\">Enter Promotion Code</h6>\r\n              <InputGroup>\r\n                <Form.Control\r\n                  type=\"text\"\r\n                  placeholder=\"Enter promotion code...\"\r\n                  value={promotionCode}\r\n                  onChange={(e) => setPromotionCode(e.target.value.toUpperCase())}\r\n                  style={{\r\n                    backgroundColor: \"rgba(255,255,255,0.1)\",\r\n                    borderColor: \"rgba(255,255,255,0.3)\",\r\n                    color: \"white\"\r\n                  }}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter') {\r\n                      handleCheckPromotionCode();\r\n                    }\r\n                  }}\r\n                />\r\n                <Button\r\n                  variant=\"outline-light\"\r\n                  onClick={handleCheckPromotionCode}\r\n                  disabled={checkingCode || !promotionCode.trim()}\r\n                >\r\n                  {checkingCode ? (\r\n                    <Spinner animation=\"border\" size=\"sm\" />\r\n                  ) : (\r\n                    <>\r\n                      <FaPlus className=\"me-1\" />\r\n                      Add\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              </InputGroup>\r\n              <small className=\"text-muted mt-1 d-block\">\r\n                Enter a promotion code to add it to your account or apply it directly\r\n              </small>\r\n            </div>\r\n\r\n            {/* Promotions section */}\r\n            <h6 className=\"mb-3\">\r\n              Available Promotions\r\n              <span className=\"small ms-2\" style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                ({promotions.filter(p => p.isValid).length} ready, {promotions.filter(p => !p.isValid).length} starting soon)\r\n              </span>\r\n            </h6>\r\n            {promotions.length === 0 ? (\r\n              <div className=\"text-center py-4\" style={{color: 'rgba(255,255,255,0.7)'}}>\r\n                <FaTag size={48} className=\"mb-3\" style={{opacity: 0.5}} />\r\n                <div>No promotions available</div>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {/* Available promotions */}\r\n                {promotions.filter(p => p.isValid).length > 0 && (\r\n                  <div className=\"row g-3 mb-4\">\r\n                    {promotions.filter(p => p.isValid).map((promotion) => (\r\n                      <div key={promotion._id} className=\"col-12\">\r\n                        <Card \r\n                          className={`promotion-card ${currentPromotionId === promotion._id ? 'current' : ''}`}\r\n                          style={{ \r\n                            backgroundColor: currentPromotionId === promotion._id ? \"rgba(40, 167, 69, 0.2)\" : \"rgba(255,255,255,0.1)\",\r\n                            borderColor: currentPromotionId === promotion._id ? \"#28a745\" : \"rgba(255,255,255,0.3)\",\r\n                            cursor: \"pointer\",\r\n                            transition: \"all 0.3s ease\"\r\n                          }}\r\n                          onClick={() => handleApplyPromotion(promotion)}\r\n                        >\r\n                          <Card.Body className=\"py-3\">\r\n                            <div className=\"d-flex justify-content-between align-items-start\">\r\n                              <div className=\"flex-grow-1\">\r\n                                <div className=\"d-flex align-items-center mb-2\">\r\n                                  <FaTag className=\"me-2 text-primary\" />\r\n                                  <h6 className=\"mb-0 fw-bold\">{promotion.code}</h6>\r\n                                  {currentPromotionId === promotion._id && (\r\n                                    <Badge bg=\"success\" className=\"ms-2\">Applied</Badge>\r\n                                  )}\r\n                                  <Badge bg=\"success\" className=\"ms-2\">Available</Badge>\r\n                                  {promotion.type === 'PRIVATE' && (\r\n                                    <Badge bg=\"warning\" className=\"ms-2\">Private</Badge>\r\n                                  )}\r\n                                </div>\r\n\r\n                                <p className=\"mb-2 small\" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>\r\n\r\n                                {/* Usage information */}\r\n                                {promotion.remainingUses !== undefined && (\r\n                                  <div className=\"mb-2\">\r\n                                    <small className=\"text-info\">\r\n                                      Remaining uses: {promotion.remainingUses}/{promotion.maxUsagePerUser}\r\n                                    </small>\r\n                                  </div>\r\n                                )}\r\n                                \r\n                                <div className=\"d-flex justify-content-between align-items-center\">\r\n                                  <div>\r\n                                    <span className=\"text-success fw-bold\">\r\n                                      Save {Utils.formatCurrency(promotion.discount)}\r\n                                    </span>\r\n                                  </div>\r\n                                  \r\n                                  <div className=\"text-end\">\r\n                                    <div className=\"small\">\r\n                                      {promotion.minOrderAmount && (\r\n                                        <div className=\"text-success\">\r\n                                          Min: {Utils.formatCurrency(promotion.minOrderAmount)} ✓\r\n                                        </div>\r\n                                      )}\r\n                                      {promotion.maxDiscount && (\r\n                                        <div style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                                          Max: {Utils.formatCurrency(promotion.maxDiscount)}\r\n                                        </div>\r\n                                      )}\r\n                                      {promotion.expiryDate && (\r\n                                        <div className=\"text-success\">\r\n                                          Expires: {new Date(promotion.expiryDate).toLocaleDateString()} ✓\r\n                                        </div>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Starting soon promotions */}\r\n                {promotions.filter(p => !p.isValid).length > 0 && (\r\n                  <>\r\n                    <h6 className=\"mb-3 text-warning\">\r\n                      Starting Soon ({promotions.filter(p => !p.isValid).length})\r\n                    </h6>\r\n                    <div className=\"row g-3\">\r\n                      {promotions.filter(p => !p.isValid).map((promotion) => (\r\n                        <div key={promotion._id} className=\"col-12\">\r\n                          <Card \r\n                            className=\"promotion-card disabled\"\r\n                            style={{ \r\n                              backgroundColor: \"rgba(255, 193, 7, 0.1)\",\r\n                              borderColor: \"rgba(255, 193, 7, 0.5)\",\r\n                              cursor: \"not-allowed\",\r\n                              opacity: 0.8,\r\n                              transition: \"all 0.3s ease\"\r\n                            }}\r\n                          >\r\n                            <Card.Body className=\"py-3\">\r\n                              <div className=\"d-flex justify-content-between align-items-start\">\r\n                                <div className=\"flex-grow-1\">\r\n                                  <div className=\"d-flex align-items-center mb-2\">\r\n                                    <FaTag className=\"me-2 text-warning\" />\r\n                                    <h6 className=\"mb-0 fw-bold\">{promotion.code}</h6>\r\n                                    <Badge bg=\"warning\" className=\"ms-2\" style={{color: 'white'}}>Starting Soon</Badge>\r\n                                  </div>\r\n                                  \r\n                                  <p className=\"mb-2 small\" style={{color: 'rgba(255,255,255,0.7)'}}>{promotion.description}</p>\r\n                                  \r\n                                  <div className=\"d-flex justify-content-between align-items-center\">\r\n                                    <div>\r\n                                      <span className=\"text-warning small fw-bold\">\r\n                                        {promotion.message}\r\n                                      </span>\r\n                                    </div>\r\n                                    \r\n                                    <div className=\"text-end\">\r\n                                      <div className=\"small\">\r\n                                        {promotion.minOrderAmount && (\r\n                                          <div className={`${totalPrice >= promotion.minOrderAmount ? 'text-success' : 'text-warning'}`}>\r\n                                            Min: {Utils.formatCurrency(promotion.minOrderAmount)}\r\n                                            {totalPrice >= promotion.minOrderAmount ? ' ✓' : ' ✗'}\r\n                                          </div>\r\n                                        )}\r\n                                        {promotion.maxDiscount && (\r\n                                          <div style={{color: 'rgba(255,255,255,0.6)'}}>\r\n                                            Max: {Utils.formatCurrency(promotion.maxDiscount)}\r\n                                          </div>\r\n                                        )}\r\n                                        {(promotion.startDate || promotion.expiryDate) && (\r\n                                          <div className=\"text-warning\">\r\n                                            Starts: {new Date(promotion.startDate || promotion.expiryDate).toLocaleDateString()}\r\n                                          </div>\r\n                                        )}\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </Card.Body>\r\n                          </Card>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </>\r\n            )}\r\n          </>\r\n        )}\r\n      </Modal.Body>\r\n      \r\n      <Modal.Footer \r\n        style={{ \r\n          backgroundColor: \"rgba(20, 30, 70, 0.95)\", \r\n          borderColor: \"rgba(255,255,255,0.2)\"\r\n        }}\r\n      >\r\n        <Button variant=\"outline-light\" onClick={onHide} disabled={applying}>\r\n          Close\r\n        </Button>\r\n      </Modal.Footer>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default PromotionModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,UAAU,QAAQ,iBAAiB;AACvF,SAASC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,SAAS,MAAM,uCAAuC;AAC7D,OAAO,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC,UAAU;EAAEC,gBAAgB;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMsC,YAAY,GAAGC,SAAS,CAAC,CAAC,CAAC,CAAC;;EAElCtC,SAAS,CAAC,MAAM;IACd,IAAImB,IAAI,IAAIE,UAAU,GAAG,CAAC,EAAE;MAC1BkB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACpB,IAAI,EAAEE,UAAU,CAAC,CAAC;EAEtB,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIY,aAAa,GAAG,EAAE;MACtB,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGR,YAAY,sBAAsB,CAAC;QAC9DO,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAACJ,KAAK,CAAC;QACrCG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,KAAK,GAAG,GAAGA,KAAK,CAACK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC;;QAE9E;QACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,GAAGZ,YAAY,sBAAsB,EAAE;UACtEa,OAAO,EAAE;YACP,eAAe,EAAE,UAAUT,KAAK;UAClC;QACF,CAAC,CAAC;QACFG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAE3CX,aAAa,GAAGO,QAAQ,CAACI,IAAI,CAAC1B,UAAU,IAAIsB,QAAQ,CAACI,IAAI,CAACA,IAAI,IAAIJ,QAAQ,CAACI,IAAI,IAAI,EAAE;QACrFP,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEL,aAAa,CAAC;QACtDI,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEE,QAAQ,CAACI,IAAI,CAAC;QAErD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACb,aAAa,CAAC,EAAE;UACjCI,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChE,MAAM,IAAIS,KAAK,CAAC,8BAA8B,CAAC;QACjD;;QAEA;QACAV,OAAO,CAACC,GAAG,CAAC,gBAAgBL,aAAa,CAACe,MAAM,aAAa,CAAC;MAChE,CAAC,CAAC,OAAOC,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjBjB,OAAO,CAACkB,KAAK,CAAC,oBAAoB,EAAE;UAClCC,OAAO,EAAEP,QAAQ,CAACO,OAAO;UACzBC,MAAM,GAAAP,kBAAA,GAAED,QAAQ,CAACT,QAAQ,cAAAU,kBAAA,uBAAjBA,kBAAA,CAAmBO,MAAM;UACjCC,UAAU,GAAAP,mBAAA,GAAEF,QAAQ,CAACT,QAAQ,cAAAW,mBAAA,uBAAjBA,mBAAA,CAAmBO,UAAU;UACzCd,IAAI,GAAAQ,mBAAA,GAAEH,QAAQ,CAACT,QAAQ,cAAAY,mBAAA,uBAAjBA,mBAAA,CAAmBR,IAAI;UAC7BD,OAAO,GAAAU,mBAAA,GAAEJ,QAAQ,CAACT,QAAQ,cAAAa,mBAAA,uBAAjBA,mBAAA,CAAmBV;QAC9B,CAAC,CAAC;;QAEF;QACA,IAAI,EAAAW,mBAAA,GAAAL,QAAQ,CAACT,QAAQ,cAAAc,mBAAA,uBAAjBA,mBAAA,CAAmBG,MAAM,MAAK,GAAG,EAAE;UACrCpB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;UAC/DnB,aAAa,CAAC,EAAE,CAAC;UACjBE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAgB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDL,aAAa,GAAG,CACd;UACE0B,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,8BAA8B;UAC3CC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,yBAAyB;UACtCC,YAAY,EAAE,YAAY;UAC1BC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,EAAE;UAClBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,WAAW;UACjBC,WAAW,EAAE,gCAAgC;UAC7CC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,GAAG;UACnBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,EACD;UACER,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE,SAAS;UACfC,WAAW,EAAE,4BAA4B;UACzCC,YAAY,EAAE,OAAO;UACrBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,EAAE;UAClBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,YAAY;UACxBC,QAAQ,EAAE;QACZ,CAAC,CACF;MACH;MAEA9B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAExB,UAAU,CAAC;MACtDuB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEL,aAAa,CAACe,MAAM,EAAE,YAAY,CAAC;;MAE7D;MACA,MAAMoB,mBAAmB,GAAGnC,aAAa,CAACoC,GAAG,CAAEC,KAAK,IAAK;QACvD;QACA,IAAIC,QAAQ,GAAG,CAAC;QAChB,IAAID,KAAK,CAACR,YAAY,KAAK,YAAY,EAAE;UACvCS,QAAQ,GAAIzD,UAAU,GAAGwD,KAAK,CAACP,aAAa,GAAI,GAAG;UACnD,IAAIO,KAAK,CAACE,iBAAiB,EAAE;YAC3BD,QAAQ,GAAGE,IAAI,CAACC,GAAG,CAACH,QAAQ,EAAED,KAAK,CAACE,iBAAiB,CAAC;UACxD;QACF,CAAC,MAAM,IAAIF,KAAK,CAACR,YAAY,KAAK,cAAc,EAAE;UAChDS,QAAQ,GAAGD,KAAK,CAACP,aAAa;QAChC;QAEA,MAAMY,aAAa,GAAG7D,UAAU,KAAKwD,KAAK,CAACN,cAAc,IAAI,CAAC,CAAC;QAC/D,MAAMY,OAAO,GAAGN,KAAK,CAACO,MAAM,IAAIF,aAAa;QAE7C,IAAInB,OAAO,GAAG,EAAE;QAChB,IAAIoB,OAAO,EAAE;UACXpB,OAAO,GAAG,QAAQnD,KAAK,CAACyE,cAAc,CAACP,QAAQ,CAAC,EAAE;QACpD,CAAC,MAAM,IAAI,CAACI,aAAa,EAAE;UACzBnB,OAAO,GAAG,iBAAiBnD,KAAK,CAACyE,cAAc,CAACR,KAAK,CAACN,cAAc,CAAC,WAAW;QAClF,CAAC,MAAM;UACLR,OAAO,GAAG,eAAe;QAC3B;QAEA,OAAO;UACL,GAAGc,KAAK;UACRM,OAAO;UACPL,QAAQ;UACRf;QACF,CAAC;MACH,CAAC,CAAC;MAEFnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE8B,mBAAmB,CAAC;;MAE/D;MACA;MACA,MAAMW,iBAAiB,GAAGX,mBAAmB,CAACY,MAAM,CAACV,KAAK,IAAI;QAC5D,MAAMW,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACZ,KAAK,CAACa,SAAS,IAAIb,KAAK,CAACJ,UAAU,IAAI,YAAY,CAAC;QAC/E,MAAMkB,OAAO,GAAG,IAAIF,IAAI,CAACZ,KAAK,CAACc,OAAO,IAAId,KAAK,CAACJ,UAAU,IAAI,YAAY,CAAC;;QAE3E;QACA,MAAMmB,UAAU,GAAGJ,GAAG,IAAIG,OAAO;QACjC,MAAMjB,QAAQ,GAAGG,KAAK,CAACH,QAAQ,KAAK,KAAK;QAEzC,OAAOkB,UAAU,IAAIlB,QAAQ;MAC/B,CAAC,CAAC;MAEF9B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyC,iBAAiB,CAAC/B,MAAM,EAAE,IAAI,EAAEoB,mBAAmB,CAACpB,MAAM,CAAC;MAC9FX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyC,iBAAiB,CAACC,MAAM,CAACM,CAAC,IAAIA,CAAC,CAACV,OAAO,CAAC,CAAC5B,MAAM,CAAC;MAC9EX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyC,iBAAiB,CAACC,MAAM,CAACM,CAAC;QAAA,IAAAC,UAAA;QAAA,OAAI,CAACD,CAAC,CAACV,OAAO,MAAAW,UAAA,GAAID,CAAC,CAAC9B,OAAO,cAAA+B,UAAA,uBAATA,UAAA,CAAWC,QAAQ,CAAC,aAAa,CAAC;MAAA,EAAC,CAACxC,MAAM,CAAC;;MAErH;MACA,MAAMyC,gBAAgB,GAAGV,iBAAiB,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACxD;QACA,IAAID,CAAC,CAACf,OAAO,IAAI,CAACgB,CAAC,CAAChB,OAAO,EAAE,OAAO,CAAC,CAAC;QACtC,IAAI,CAACe,CAAC,CAACf,OAAO,IAAIgB,CAAC,CAAChB,OAAO,EAAE,OAAO,CAAC;;QAErC;QACA,OAAOgB,CAAC,CAACrB,QAAQ,GAAGoB,CAAC,CAACpB,QAAQ;MAChC,CAAC,CAAC;MAEFpD,aAAa,CAACsE,gBAAgB,CAAC;IACjC,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDpC,aAAa,CAAC,EAAE,CAAC;IACnB;IACAE,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMwE,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI,CAACA,SAAS,CAAClB,OAAO,EAAE;IAExBnD,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,IAAI;QACF;QACA,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAACsD,IAAI,CAAC,GAAGjE,YAAY,uBAAuB,EAAE;UACxE8B,IAAI,EAAEkC,SAAS,CAAClC,IAAI;UACpBoC,WAAW,EAAElF;QACf,CAAC,CAAC;QAEF,IAAI0B,QAAQ,CAACI,IAAI,CAACqD,KAAK,EAAE;UACvBlF,gBAAgB,CAAC;YACf6C,IAAI,EAAEkC,SAAS,CAAClC,IAAI;YACpBW,QAAQ,EAAE/B,QAAQ,CAACI,IAAI,CAAC2B,QAAQ;YAChCf,OAAO,EAAE,uBAAuBnD,KAAK,CAACyE,cAAc,CAACtC,QAAQ,CAACI,IAAI,CAAC2B,QAAQ,CAAC,EAAE;YAC9E2B,WAAW,EAAE1D,QAAQ,CAACI,IAAI,CAACsD;UAC7B,CAAC,CAAC;UACFrF,MAAM,CAAC,CAAC;QACV;MACF,CAAC,CAAC,OAAOoC,QAAQ,EAAE;QACjB;QACAZ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CvB,gBAAgB,CAAC;UACf6C,IAAI,EAAEkC,SAAS,CAAClC,IAAI;UACpBW,QAAQ,EAAEuB,SAAS,CAACvB,QAAQ;UAC5Bf,OAAO,EAAE,uBAAuBnD,KAAK,CAACyE,cAAc,CAACgB,SAAS,CAACvB,QAAQ,CAAC,EAAE;UAC1E2B,WAAW,EAAEJ,SAAS,CAACnC;QACzB,CAAC,CAAC;QACF9C,MAAM,CAAC,CAAC;MACV;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;IACA9B,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAM0E,qBAAqB,GAAGA,CAAA,KAAM;IAClCpF,gBAAgB,CAAC;MACf6C,IAAI,EAAE,EAAE;MACRW,QAAQ,EAAE,CAAC;MACXf,OAAO,EAAE,EAAE;MACX0C,WAAW,EAAE;IACf,CAAC,CAAC;IACFrF,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAMuF,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI,CAAC1E,aAAa,CAAC2E,IAAI,CAAC,CAAC,EAAE;IAE3BxE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF;MACA,MAAMyE,aAAa,GAAG,MAAM7D,KAAK,CAACsD,IAAI,CAAC,GAAGjE,YAAY,+BAA+B,EAAE;QACrF8B,IAAI,EAAElC,aAAa,CAAC2E,IAAI,CAAC;MAC3B,CAAC,EAAE;QACD1D,OAAO,EAAE;UACP,eAAe,EAAE,UAAUR,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIkE,aAAa,CAAC1D,IAAI,CAAC2D,OAAO,EAAE;QAC9B;QACA,MAAMvE,eAAe,CAAC,CAAC;QACvBL,gBAAgB,CAAC,EAAE,CAAC;QACpB6E,KAAK,CAAC,0CAA0C,CAAC;MACnD;IACF,CAAC,CAAC,OAAOjD,KAAK,EAAE;MAAA,IAAAkD,eAAA;MACd,IAAI,EAAAA,eAAA,GAAAlD,KAAK,CAACf,QAAQ,cAAAiE,eAAA,uBAAdA,eAAA,CAAgBhD,MAAM,MAAK,GAAG,EAAE;QAClC;QACA,IAAI;UACF,MAAMiD,aAAa,GAAG,MAAMjE,KAAK,CAACsD,IAAI,CAAC,GAAGjE,YAAY,uBAAuB,EAAE;YAC7E8B,IAAI,EAAElC,aAAa,CAAC2E,IAAI,CAAC,CAAC;YAC1BL,WAAW,EAAElF;UACf,CAAC,EAAE;YACD6B,OAAO,EAAE;cACP,eAAe,EAAE,UAAUR,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC1D;UACF,CAAC,CAAC;UAEF,IAAIsE,aAAa,CAAC9D,IAAI,CAACqD,KAAK,EAAE;YAC5BlF,gBAAgB,CAAC;cACf6C,IAAI,EAAElC,aAAa,CAAC2E,IAAI,CAAC,CAAC;cAC1B9B,QAAQ,EAAEmC,aAAa,CAAC9D,IAAI,CAAC2B,QAAQ;cACrCf,OAAO,EAAE,uBAAuBnD,KAAK,CAACyE,cAAc,CAAC4B,aAAa,CAAC9D,IAAI,CAAC2B,QAAQ,CAAC,EAAE;cACnF2B,WAAW,EAAEQ,aAAa,CAAC9D,IAAI,CAACsD;YAClC,CAAC,CAAC;YACFrF,MAAM,CAAC,CAAC;UACV;QACF,CAAC,CAAC,OAAO8F,UAAU,EAAE;UAAA,IAAAC,oBAAA,EAAAC,qBAAA;UACnBL,KAAK,CAAC,EAAAI,oBAAA,GAAAD,UAAU,CAACnE,QAAQ,cAAAoE,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqBhE,IAAI,cAAAiE,qBAAA,uBAAzBA,qBAAA,CAA2BrD,OAAO,KAAI,wBAAwB,CAAC;QACvE;MACF,CAAC,MAAM;QAAA,IAAAsD,gBAAA,EAAAC,qBAAA;QACLP,KAAK,CAAC,EAAAM,gBAAA,GAAAvD,KAAK,CAACf,QAAQ,cAAAsE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlE,IAAI,cAAAmE,qBAAA,uBAApBA,qBAAA,CAAsBvD,OAAO,KAAI,+BAA+B,CAAC;MACzE;IACF;IACA3B,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACErB,OAAA,CAACd,KAAK;IAACkB,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAACmG,IAAI,EAAC,IAAI;IAACC,QAAQ;IAAAC,QAAA,gBACnD1G,OAAA,CAACd,KAAK,CAACyH,MAAM;MACXC,WAAW;MACXC,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE,uBAAuB;QACpCC,KAAK,EAAE;MACT,CAAE;MAAAN,QAAA,eAEF1G,OAAA,CAACd,KAAK,CAAC+H,KAAK;QAACC,SAAS,EAAC,2BAA2B;QAAAR,QAAA,gBAChD1G,OAAA,CAACP,KAAK;UAACyH,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE5B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEftH,OAAA,CAACd,KAAK,CAACqI,IAAI;MACTV,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCE,KAAK,EAAE,OAAO;QACdQ,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;MACb,CAAE;MAAAf,QAAA,EAED9F,OAAO,gBACNZ,OAAA;QAAKkH,SAAS,EAAC,kBAAkB;QAAAR,QAAA,gBAC/B1G,OAAA,CAACV,OAAO;UAACoI,SAAS,EAAC,QAAQ;UAACC,OAAO,EAAC;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CtH,OAAA;UAAKkH,SAAS,EAAC,MAAM;UAAAR,QAAA,EAAC;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,gBAENtH,OAAA,CAAAE,SAAA;QAAAwG,QAAA,GAEGlG,kBAAkB,iBACjBR,OAAA;UAAKkH,SAAS,EAAC,MAAM;UAAAR,QAAA,gBACnB1G,OAAA;YAAIkH,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAC;UAAyB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDtH,OAAA,CAACZ,IAAI;YACH8H,SAAS,EAAC,kCAAkC;YAC5CL,KAAK,EAAE;cACLC,eAAe,EAAE,wBAAwB;cACzCC,WAAW,EAAE,SAAS;cACtBa,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEF1G,OAAA,CAACZ,IAAI,CAACmI,IAAI;cAACL,SAAS,EAAC,MAAM;cAAAR,QAAA,eACzB1G,OAAA;gBAAKkH,SAAS,EAAC,mDAAmD;gBAAAR,QAAA,gBAChE1G,OAAA;kBAAKkH,SAAS,EAAC,2BAA2B;kBAAAR,QAAA,gBACxC1G,OAAA,CAACL,OAAO;oBAACuH,SAAS,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzCtH,OAAA;oBAAMkH,SAAS,EAAC,sBAAsB;oBAAAR,QAAA,EAAC;kBAAO;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNtH,OAAA,CAACb,MAAM;kBACLwI,OAAO,EAAC,gBAAgB;kBACxBnB,IAAI,EAAC,IAAI;kBACTqB,OAAO,EAAElC,qBAAsB;kBAC/BmC,QAAQ,EAAE9G,QAAS;kBAAA0F,QAAA,gBAEnB1G,OAAA,CAACN,OAAO;oBAACwH,SAAS,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAE9B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGDtH,OAAA;UAAKkH,SAAS,EAAC,MAAM;UAAAR,QAAA,gBACnB1G,OAAA;YAAIkH,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAC;UAAoB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CtH,OAAA,CAACR,UAAU;YAAAkH,QAAA,gBACT1G,OAAA,CAACT,IAAI,CAACwI,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,yBAAyB;cACrCC,KAAK,EAAEhH,aAAc;cACrBiH,QAAQ,EAAGC,CAAC,IAAKjH,gBAAgB,CAACiH,CAAC,CAACC,MAAM,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,CAAE;cAChEzB,KAAK,EAAE;gBACLC,eAAe,EAAE,uBAAuB;gBACxCC,WAAW,EAAE,uBAAuB;gBACpCC,KAAK,EAAE;cACT,CAAE;cACFuB,SAAS,EAAGH,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACI,GAAG,KAAK,OAAO,EAAE;kBACrB5C,wBAAwB,CAAC,CAAC;gBAC5B;cACF;YAAE;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFtH,OAAA,CAACb,MAAM;cACLwI,OAAO,EAAC,eAAe;cACvBE,OAAO,EAAEjC,wBAAyB;cAClCkC,QAAQ,EAAE1G,YAAY,IAAI,CAACF,aAAa,CAAC2E,IAAI,CAAC,CAAE;cAAAa,QAAA,EAE/CtF,YAAY,gBACXpB,OAAA,CAACV,OAAO;gBAACoI,SAAS,EAAC,QAAQ;gBAAClB,IAAI,EAAC;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExCtH,OAAA,CAAAE,SAAA;gBAAAwG,QAAA,gBACE1G,OAAA,CAACJ,MAAM;kBAACsH,SAAS,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,OAE7B;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACbtH,OAAA;YAAOkH,SAAS,EAAC,yBAAyB;YAAAR,QAAA,EAAC;UAE3C;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNtH,OAAA;UAAIkH,SAAS,EAAC,MAAM;UAAAR,QAAA,GAAC,sBAEnB,eAAA1G,OAAA;YAAMkH,SAAS,EAAC,YAAY;YAACL,KAAK,EAAE;cAACG,KAAK,EAAE;YAAuB,CAAE;YAAAN,QAAA,GAAC,GACnE,EAAChG,UAAU,CAAC8D,MAAM,CAACM,CAAC,IAAIA,CAAC,CAACV,OAAO,CAAC,CAAC5B,MAAM,EAAC,UAAQ,EAAC9B,UAAU,CAAC8D,MAAM,CAACM,CAAC,IAAI,CAACA,CAAC,CAACV,OAAO,CAAC,CAAC5B,MAAM,EAAC,iBAChG;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACJ5G,UAAU,CAAC8B,MAAM,KAAK,CAAC,gBACtBxC,OAAA;UAAKkH,SAAS,EAAC,kBAAkB;UAACL,KAAK,EAAE;YAACG,KAAK,EAAE;UAAuB,CAAE;UAAAN,QAAA,gBACxE1G,OAAA,CAACP,KAAK;YAAC+G,IAAI,EAAE,EAAG;YAACU,SAAS,EAAC,MAAM;YAACL,KAAK,EAAE;cAAC4B,OAAO,EAAE;YAAG;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3DtH,OAAA;YAAA0G,QAAA,EAAK;UAAuB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,gBAENtH,OAAA,CAAAE,SAAA;UAAAwG,QAAA,GAEGhG,UAAU,CAAC8D,MAAM,CAACM,CAAC,IAAIA,CAAC,CAACV,OAAO,CAAC,CAAC5B,MAAM,GAAG,CAAC,iBAC3CxC,OAAA;YAAKkH,SAAS,EAAC,cAAc;YAAAR,QAAA,EAC1BhG,UAAU,CAAC8D,MAAM,CAACM,CAAC,IAAIA,CAAC,CAACV,OAAO,CAAC,CAACP,GAAG,CAAEyB,SAAS,iBAC/CtF,OAAA;cAAyBkH,SAAS,EAAC,QAAQ;cAAAR,QAAA,eACzC1G,OAAA,CAACZ,IAAI;gBACH8H,SAAS,EAAE,kBAAkB1G,kBAAkB,KAAK8E,SAAS,CAACnC,GAAG,GAAG,SAAS,GAAG,EAAE,EAAG;gBACrF0D,KAAK,EAAE;kBACLC,eAAe,EAAEtG,kBAAkB,KAAK8E,SAAS,CAACnC,GAAG,GAAG,wBAAwB,GAAG,uBAAuB;kBAC1G4D,WAAW,EAAEvG,kBAAkB,KAAK8E,SAAS,CAACnC,GAAG,GAAG,SAAS,GAAG,uBAAuB;kBACvFuF,MAAM,EAAE,SAAS;kBACjBC,UAAU,EAAE;gBACd,CAAE;gBACFd,OAAO,EAAEA,CAAA,KAAMxC,oBAAoB,CAACC,SAAS,CAAE;gBAAAoB,QAAA,eAE/C1G,OAAA,CAACZ,IAAI,CAACmI,IAAI;kBAACL,SAAS,EAAC,MAAM;kBAAAR,QAAA,eACzB1G,OAAA;oBAAKkH,SAAS,EAAC,kDAAkD;oBAAAR,QAAA,eAC/D1G,OAAA;sBAAKkH,SAAS,EAAC,aAAa;sBAAAR,QAAA,gBAC1B1G,OAAA;wBAAKkH,SAAS,EAAC,gCAAgC;wBAAAR,QAAA,gBAC7C1G,OAAA,CAACP,KAAK;0BAACyH,SAAS,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACvCtH,OAAA;0BAAIkH,SAAS,EAAC,cAAc;0BAAAR,QAAA,EAAEpB,SAAS,CAAClC;wBAAI;0BAAA+D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,EACjD9G,kBAAkB,KAAK8E,SAAS,CAACnC,GAAG,iBACnCnD,OAAA,CAACX,KAAK;0BAACuJ,EAAE,EAAC,SAAS;0BAAC1B,SAAS,EAAC,MAAM;0BAAAR,QAAA,EAAC;wBAAO;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CACpD,eACDtH,OAAA,CAACX,KAAK;0BAACuJ,EAAE,EAAC,SAAS;0BAAC1B,SAAS,EAAC,MAAM;0BAAAR,QAAA,EAAC;wBAAS;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACrDhC,SAAS,CAAC0C,IAAI,KAAK,SAAS,iBAC3BhI,OAAA,CAACX,KAAK;0BAACuJ,EAAE,EAAC,SAAS;0BAAC1B,SAAS,EAAC,MAAM;0BAAAR,QAAA,EAAC;wBAAO;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CACpD;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENtH,OAAA;wBAAGkH,SAAS,EAAC,YAAY;wBAACL,KAAK,EAAE;0BAACG,KAAK,EAAE;wBAAuB,CAAE;wBAAAN,QAAA,EAAEpB,SAAS,CAACjC;sBAAW;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAG7FhC,SAAS,CAACuD,aAAa,KAAKC,SAAS,iBACpC9I,OAAA;wBAAKkH,SAAS,EAAC,MAAM;wBAAAR,QAAA,eACnB1G,OAAA;0BAAOkH,SAAS,EAAC,WAAW;0BAAAR,QAAA,GAAC,kBACX,EAACpB,SAAS,CAACuD,aAAa,EAAC,GAAC,EAACvD,SAAS,CAACyD,eAAe;wBAAA;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CACN,eAEDtH,OAAA;wBAAKkH,SAAS,EAAC,mDAAmD;wBAAAR,QAAA,gBAChE1G,OAAA;0BAAA0G,QAAA,eACE1G,OAAA;4BAAMkH,SAAS,EAAC,sBAAsB;4BAAAR,QAAA,GAAC,OAChC,EAAC7G,KAAK,CAACyE,cAAc,CAACgB,SAAS,CAACvB,QAAQ,CAAC;0BAAA;4BAAAoD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eAENtH,OAAA;0BAAKkH,SAAS,EAAC,UAAU;0BAAAR,QAAA,eACvB1G,OAAA;4BAAKkH,SAAS,EAAC,OAAO;4BAAAR,QAAA,GACnBpB,SAAS,CAAC9B,cAAc,iBACvBxD,OAAA;8BAAKkH,SAAS,EAAC,cAAc;8BAAAR,QAAA,GAAC,OACvB,EAAC7G,KAAK,CAACyE,cAAc,CAACgB,SAAS,CAAC9B,cAAc,CAAC,EAAC,SACvD;4BAAA;8BAAA2D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN,EACAhC,SAAS,CAAC7B,WAAW,iBACpBzD,OAAA;8BAAK6G,KAAK,EAAE;gCAACG,KAAK,EAAE;8BAAuB,CAAE;8BAAAN,QAAA,GAAC,OACvC,EAAC7G,KAAK,CAACyE,cAAc,CAACgB,SAAS,CAAC7B,WAAW,CAAC;4BAAA;8BAAA0D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9C,CACN,EACAhC,SAAS,CAAC5B,UAAU,iBACnB1D,OAAA;8BAAKkH,SAAS,EAAC,cAAc;8BAAAR,QAAA,GAAC,WACnB,EAAC,IAAIhC,IAAI,CAACY,SAAS,CAAC5B,UAAU,CAAC,CAACsF,kBAAkB,CAAC,CAAC,EAAC,SAChE;4BAAA;8BAAA7B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CACN;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC,GAnEChC,SAAS,CAACnC,GAAG;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoElB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EAGA5G,UAAU,CAAC8D,MAAM,CAACM,CAAC,IAAI,CAACA,CAAC,CAACV,OAAO,CAAC,CAAC5B,MAAM,GAAG,CAAC,iBAC5CxC,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA;cAAIkH,SAAS,EAAC,mBAAmB;cAAAR,QAAA,GAAC,iBACjB,EAAChG,UAAU,CAAC8D,MAAM,CAACM,CAAC,IAAI,CAACA,CAAC,CAACV,OAAO,CAAC,CAAC5B,MAAM,EAAC,GAC5D;YAAA;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtH,OAAA;cAAKkH,SAAS,EAAC,SAAS;cAAAR,QAAA,EACrBhG,UAAU,CAAC8D,MAAM,CAACM,CAAC,IAAI,CAACA,CAAC,CAACV,OAAO,CAAC,CAACP,GAAG,CAAEyB,SAAS,iBAChDtF,OAAA;gBAAyBkH,SAAS,EAAC,QAAQ;gBAAAR,QAAA,eACzC1G,OAAA,CAACZ,IAAI;kBACH8H,SAAS,EAAC,yBAAyB;kBACnCL,KAAK,EAAE;oBACLC,eAAe,EAAE,wBAAwB;oBACzCC,WAAW,EAAE,wBAAwB;oBACrC2B,MAAM,EAAE,aAAa;oBACrBD,OAAO,EAAE,GAAG;oBACZE,UAAU,EAAE;kBACd,CAAE;kBAAAjC,QAAA,eAEF1G,OAAA,CAACZ,IAAI,CAACmI,IAAI;oBAACL,SAAS,EAAC,MAAM;oBAAAR,QAAA,eACzB1G,OAAA;sBAAKkH,SAAS,EAAC,kDAAkD;sBAAAR,QAAA,eAC/D1G,OAAA;wBAAKkH,SAAS,EAAC,aAAa;wBAAAR,QAAA,gBAC1B1G,OAAA;0BAAKkH,SAAS,EAAC,gCAAgC;0BAAAR,QAAA,gBAC7C1G,OAAA,CAACP,KAAK;4BAACyH,SAAS,EAAC;0BAAmB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACvCtH,OAAA;4BAAIkH,SAAS,EAAC,cAAc;4BAAAR,QAAA,EAAEpB,SAAS,CAAClC;0BAAI;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAClDtH,OAAA,CAACX,KAAK;4BAACuJ,EAAE,EAAC,SAAS;4BAAC1B,SAAS,EAAC,MAAM;4BAACL,KAAK,EAAE;8BAACG,KAAK,EAAE;4BAAO,CAAE;4BAAAN,QAAA,EAAC;0BAAa;4BAAAS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChF,CAAC,eAENtH,OAAA;0BAAGkH,SAAS,EAAC,YAAY;0BAACL,KAAK,EAAE;4BAACG,KAAK,EAAE;0BAAuB,CAAE;0BAAAN,QAAA,EAAEpB,SAAS,CAACjC;wBAAW;0BAAA8D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAE9FtH,OAAA;0BAAKkH,SAAS,EAAC,mDAAmD;0BAAAR,QAAA,gBAChE1G,OAAA;4BAAA0G,QAAA,eACE1G,OAAA;8BAAMkH,SAAS,EAAC,4BAA4B;8BAAAR,QAAA,EACzCpB,SAAS,CAACtC;4BAAO;8BAAAmE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACd;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC,eAENtH,OAAA;4BAAKkH,SAAS,EAAC,UAAU;4BAAAR,QAAA,eACvB1G,OAAA;8BAAKkH,SAAS,EAAC,OAAO;8BAAAR,QAAA,GACnBpB,SAAS,CAAC9B,cAAc,iBACvBxD,OAAA;gCAAKkH,SAAS,EAAE,GAAG5G,UAAU,IAAIgF,SAAS,CAAC9B,cAAc,GAAG,cAAc,GAAG,cAAc,EAAG;gCAAAkD,QAAA,GAAC,OACxF,EAAC7G,KAAK,CAACyE,cAAc,CAACgB,SAAS,CAAC9B,cAAc,CAAC,EACnDlD,UAAU,IAAIgF,SAAS,CAAC9B,cAAc,GAAG,IAAI,GAAG,IAAI;8BAAA;gCAAA2D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClD,CACN,EACAhC,SAAS,CAAC7B,WAAW,iBACpBzD,OAAA;gCAAK6G,KAAK,EAAE;kCAACG,KAAK,EAAE;gCAAuB,CAAE;gCAAAN,QAAA,GAAC,OACvC,EAAC7G,KAAK,CAACyE,cAAc,CAACgB,SAAS,CAAC7B,WAAW,CAAC;8BAAA;gCAAA0D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9C,CACN,EACA,CAAChC,SAAS,CAACX,SAAS,IAAIW,SAAS,CAAC5B,UAAU,kBAC3C1D,OAAA;gCAAKkH,SAAS,EAAC,cAAc;gCAAAR,QAAA,GAAC,UACpB,EAAC,IAAIhC,IAAI,CAACY,SAAS,CAACX,SAAS,IAAIW,SAAS,CAAC5B,UAAU,CAAC,CAACsF,kBAAkB,CAAC,CAAC;8BAAA;gCAAA7B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChF,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC,GArDChC,SAAS,CAACnC,GAAG;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsDlB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,eACN,CACH;QAAA,eACD,CACH;MAAA,eACD;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAEbtH,OAAA,CAACd,KAAK,CAAC+J,MAAM;MACXpC,KAAK,EAAE;QACLC,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE;MACf,CAAE;MAAAL,QAAA,eAEF1G,OAAA,CAACb,MAAM;QAACwI,OAAO,EAAC,eAAe;QAACE,OAAO,EAAExH,MAAO;QAACyH,QAAQ,EAAE9G,QAAS;QAAA0F,QAAA,EAAC;MAErE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;AAAC7G,EAAA,CAvjBIN,cAAc;AAAA+I,EAAA,GAAd/I,cAAc;AAyjBpB,eAAeA,cAAc;AAAC,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}