{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\nvar getSideChannelMap = require('side-channel-map');\nvar $TypeError = require('es-errors/type');\nvar $WeakMap = GetIntrinsic('%WeakMap%', true);\n\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => V} */\nvar $weakMapGet = callBound('WeakMap.prototype.get', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K, value: V) => void} */\nvar $weakMapSet = callBound('WeakMap.prototype.set', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapHas = callBound('WeakMap.prototype.has', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapDelete = callBound('WeakMap.prototype.delete', true);\n\n/** @type {import('.')} */\nmodule.exports = $WeakMap ? /** @type {Exclude<import('.'), false>} */function getSideChannelWeakMap() {\n  /** @typedef {ReturnType<typeof getSideChannelWeakMap>} Channel */\n  /** @typedef {Parameters<Channel['get']>[0]} K */\n  /** @typedef {Parameters<Channel['set']>[1]} V */\n\n  /** @type {WeakMap<K & object, V> | undefined} */var $wm;\n  /** @type {Channel | undefined} */\n  var $m;\n\n  /** @type {Channel} */\n  var channel = {\n    assert: function (key) {\n      if (!channel.has(key)) {\n        throw new $TypeError('Side channel does not contain ' + inspect(key));\n      }\n    },\n    'delete': function (key) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if ($wm) {\n          return $weakMapDelete($wm, key);\n        }\n      } else if (getSideChannelMap) {\n        if ($m) {\n          return $m['delete'](key);\n        }\n      }\n      return false;\n    },\n    get: function (key) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if ($wm) {\n          return $weakMapGet($wm, key);\n        }\n      }\n      return $m && $m.get(key);\n    },\n    has: function (key) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if ($wm) {\n          return $weakMapHas($wm, key);\n        }\n      }\n      return !!$m && $m.has(key);\n    },\n    set: function (key, value) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if (!$wm) {\n          $wm = new $WeakMap();\n        }\n        $weakMapSet($wm, key, value);\n      } else if (getSideChannelMap) {\n        if (!$m) {\n          $m = getSideChannelMap();\n        }\n        // eslint-disable-next-line no-extra-parens\n        /** @type {NonNullable<typeof $m>} */\n        $m.set(key, value);\n      }\n    }\n  };\n\n  // @ts-expect-error TODO: figure out why this is erroring\n  return channel;\n} : getSideChannelMap;", "map": {"version": 3, "names": ["GetIntrinsic", "require", "callBound", "inspect", "getSideChannelMap", "$TypeError", "$WeakMap", "$weakMapGet", "$weakMapSet", "$weakMapHas", "$weakMapDelete", "module", "exports", "getSideChannelWeakMap", "$wm", "$m", "channel", "assert", "key", "has", "delete", "get", "set", "value"], "sources": ["E:/Uroom/Admin/node_modules/side-channel-weakmap/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\nvar getSideChannelMap = require('side-channel-map');\n\nvar $TypeError = require('es-errors/type');\nvar $WeakMap = GetIntrinsic('%WeakMap%', true);\n\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => V} */\nvar $weakMapGet = callBound('WeakMap.prototype.get', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K, value: V) => void} */\nvar $weakMapSet = callBound('WeakMap.prototype.set', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapHas = callBound('WeakMap.prototype.has', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapDelete = callBound('WeakMap.prototype.delete', true);\n\n/** @type {import('.')} */\nmodule.exports = $WeakMap\n\t? /** @type {Exclude<import('.'), false>} */ function getSideChannelWeakMap() {\n\t\t/** @typedef {ReturnType<typeof getSideChannelWeakMap>} Channel */\n\t\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t\t/** @type {WeakMap<K & object, V> | undefined} */ var $wm;\n\t\t/** @type {Channel | undefined} */ var $m;\n\n\t\t/** @type {Channel} */\n\t\tvar channel = {\n\t\t\tassert: function (key) {\n\t\t\t\tif (!channel.has(key)) {\n\t\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t\t}\n\t\t\t},\n\t\t\t'delete': function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapDelete($wm, key);\n\t\t\t\t\t}\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif ($m) {\n\t\t\t\t\t\treturn $m['delete'](key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tget: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapGet($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn $m && $m.get(key);\n\t\t\t},\n\t\t\thas: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapHas($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn !!$m && $m.has(key);\n\t\t\t},\n\t\t\tset: function (key, value) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif (!$wm) {\n\t\t\t\t\t\t$wm = new $WeakMap();\n\t\t\t\t\t}\n\t\t\t\t\t$weakMapSet($wm, key, value);\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif (!$m) {\n\t\t\t\t\t\t$m = getSideChannelMap();\n\t\t\t\t\t}\n\t\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\t\t/** @type {NonNullable<typeof $m>} */ ($m).set(key, value);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t// @ts-expect-error TODO: figure out why this is erroring\n\t\treturn channel;\n\t}\n\t: getSideChannelMap;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIC,SAAS,GAAGD,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIE,OAAO,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AACvC,IAAIG,iBAAiB,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAEnD,IAAII,UAAU,GAAGJ,OAAO,CAAC,gBAAgB,CAAC;AAC1C,IAAIK,QAAQ,GAAGN,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;;AAE9C;AACA,IAAIO,WAAW,GAAGL,SAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC;AAC1D;AACA,IAAIM,WAAW,GAAGN,SAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC;AAC1D;AACA,IAAIO,WAAW,GAAGP,SAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC;AAC1D;AACA,IAAIQ,cAAc,GAAGR,SAAS,CAAC,0BAA0B,EAAE,IAAI,CAAC;;AAEhE;AACAS,MAAM,CAACC,OAAO,GAAGN,QAAQ,GACtB,0CAA2C,SAASO,qBAAqBA,CAAA,EAAG;EAC7E;EACA;EACA;;EAEA,iDAAkD,IAAIC,GAAG;EACzD;EAAmC,IAAIC,EAAE;;EAEzC;EACA,IAAIC,OAAO,GAAG;IACbC,MAAM,EAAE,SAAAA,CAAUC,GAAG,EAAE;MACtB,IAAI,CAACF,OAAO,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;QACtB,MAAM,IAAIb,UAAU,CAAC,gCAAgC,GAAGF,OAAO,CAACe,GAAG,CAAC,CAAC;MACtE;IACD,CAAC;IACD,QAAQ,EAAE,SAAAE,CAAUF,GAAG,EAAE;MACxB,IAAIZ,QAAQ,IAAIY,GAAG,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,EAAE;QAC9E,IAAIJ,GAAG,EAAE;UACR,OAAOJ,cAAc,CAACI,GAAG,EAAEI,GAAG,CAAC;QAChC;MACD,CAAC,MAAM,IAAId,iBAAiB,EAAE;QAC7B,IAAIW,EAAE,EAAE;UACP,OAAOA,EAAE,CAAC,QAAQ,CAAC,CAACG,GAAG,CAAC;QACzB;MACD;MACA,OAAO,KAAK;IACb,CAAC;IACDG,GAAG,EAAE,SAAAA,CAAUH,GAAG,EAAE;MACnB,IAAIZ,QAAQ,IAAIY,GAAG,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,EAAE;QAC9E,IAAIJ,GAAG,EAAE;UACR,OAAOP,WAAW,CAACO,GAAG,EAAEI,GAAG,CAAC;QAC7B;MACD;MACA,OAAOH,EAAE,IAAIA,EAAE,CAACM,GAAG,CAACH,GAAG,CAAC;IACzB,CAAC;IACDC,GAAG,EAAE,SAAAA,CAAUD,GAAG,EAAE;MACnB,IAAIZ,QAAQ,IAAIY,GAAG,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,EAAE;QAC9E,IAAIJ,GAAG,EAAE;UACR,OAAOL,WAAW,CAACK,GAAG,EAAEI,GAAG,CAAC;QAC7B;MACD;MACA,OAAO,CAAC,CAACH,EAAE,IAAIA,EAAE,CAACI,GAAG,CAACD,GAAG,CAAC;IAC3B,CAAC;IACDI,GAAG,EAAE,SAAAA,CAAUJ,GAAG,EAAEK,KAAK,EAAE;MAC1B,IAAIjB,QAAQ,IAAIY,GAAG,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,EAAE;QAC9E,IAAI,CAACJ,GAAG,EAAE;UACTA,GAAG,GAAG,IAAIR,QAAQ,CAAC,CAAC;QACrB;QACAE,WAAW,CAACM,GAAG,EAAEI,GAAG,EAAEK,KAAK,CAAC;MAC7B,CAAC,MAAM,IAAInB,iBAAiB,EAAE;QAC7B,IAAI,CAACW,EAAE,EAAE;UACRA,EAAE,GAAGX,iBAAiB,CAAC,CAAC;QACzB;QACA;QACA;QAAuCW,EAAE,CAAEO,GAAG,CAACJ,GAAG,EAAEK,KAAK,CAAC;MAC3D;IACD;EACD,CAAC;;EAED;EACA,OAAOP,OAAO;AACf,CAAC,GACCZ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}