const mongoose = require('mongoose');
const Promotion = require('./src/models/Promotion');
const PromotionUser = require('./src/models/PromotionUser');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hotel_booking');
    console.log('MongoDB connected');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedPromotions = async () => {
  try {
    // Xóa dữ liệu cũ
    await Promotion.deleteMany({});
    await PromotionUser.deleteMany({});

    // Tạo promotions
    const promotions = await Promotion.insertMany([
      {
        code: "SUMMER10",
        name: "Summer Sale 10%",
        description: "Enjoy 10% off on all products during summer!",
        discountType: "PERCENTAGE",
        discountValue: 10,
        maxDiscountAmount: 20,
        minOrderAmount: 100,
        startDate: new Date("2025-06-01"),
        endDate: new Date("2025-07-01"),
        usageLimit: 100,
        type: "PUBLIC",
        maxUsagePerUser: 2,
      },
      {
        code: "VIP100",
        name: "VIP Member Exclusive",
        description: "Special $100 discount for VIP members only",
        discountType: "FIXED_AMOUNT",
        discountValue: 100,
        minOrderAmount: 500,
        startDate: new Date("2025-01-01"),
        endDate: new Date("2025-12-31"),
        usageLimit: 10,
        type: "PRIVATE",
        maxUsagePerUser: 1,
      }
    ]);

    // Assign private promotion cho user 11
    await PromotionUser.create({
      promotionId: promotions[1]._id, // VIP100
      userId: 11,
      usedCount: 0,
    });

    console.log('Promotions seeded successfully');
  } catch (error) {
    console.error('Error seeding promotions:', error);
  }
};

const runSeed = async () => {
  await connectDB();
  await seedPromotions();
  await mongoose.connection.close();
  console.log('Seed completed');
};

runSeed();
