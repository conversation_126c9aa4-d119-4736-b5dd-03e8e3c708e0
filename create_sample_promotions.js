// <PERSON><PERSON>t to create sample promotions for testing
// Run this in MongoDB or through your backend

const samplePromotions = [
  {
    code: "WELCOME20",
    name: "Welcome 20% Off",
    description: "20% discount for new customers",
    discountType: "PERCENTAGE",
    discountValue: 20,
    maxDiscountAmount: 50,
    minOrderAmount: 100,
    startDate: new Date("2025-01-01"),
    endDate: new Date("2025-12-31"),
    usageLimit: 1000,
    type: "PUBLIC",
    maxUsagePerUser: 3,
    isActive: true
  },
  {
    code: "SAVE50",
    name: "Save $50",
    description: "Fixed $50 discount on orders over $200",
    discountType: "FIXED_AMOUNT",
    discountValue: 50,
    minOrderAmount: 200,
    startDate: new Date("2025-01-01"),
    endDate: new Date("2025-12-31"),
    usageLimit: 500,
    type: "PUBLIC",
    maxUsagePerUser: 2,
    isActive: true
  },
  {
    code: "VIP30",
    name: "VIP 30% Off",
    description: "Exclusive 30% discount for VIP members",
    discountType: "PERCENTAGE",
    discountValue: 30,
    maxDiscountAmount: 100,
    minOrderAmount: 150,
    startDate: new Date("2025-01-01"),
    endDate: new Date("2025-12-31"),
    usageLimit: 100,
    type: "PRIVATE",
    maxUsagePerUser: 5,
    isActive: true
  }
];

// MongoDB commands to insert:
/*
use your_database_name;

db.promotions.insertMany([
  {
    code: "WELCOME20",
    name: "Welcome 20% Off",
    description: "20% discount for new customers",
    discountType: "PERCENTAGE",
    discountValue: 20,
    maxDiscountAmount: 50,
    minOrderAmount: 100,
    startDate: new Date("2025-01-01"),
    endDate: new Date("2025-12-31"),
    usageLimit: 1000,
    type: "PUBLIC",
    maxUsagePerUser: 3,
    isActive: true,
    usedCount: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    code: "SAVE50",
    name: "Save $50",
    description: "Fixed $50 discount on orders over $200",
    discountType: "FIXED_AMOUNT",
    discountValue: 50,
    minOrderAmount: 200,
    startDate: new Date("2025-01-01"),
    endDate: new Date("2025-12-31"),
    usageLimit: 500,
    type: "PUBLIC",
    maxUsagePerUser: 2,
    isActive: true,
    usedCount: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    code: "VIP30",
    name: "VIP 30% Off",
    description: "Exclusive 30% discount for VIP members",
    discountType: "PERCENTAGE",
    discountValue: 30,
    maxDiscountAmount: 100,
    minOrderAmount: 150,
    startDate: new Date("2025-01-01"),
    endDate: new Date("2025-12-31"),
    usageLimit: 100,
    type: "PRIVATE",
    maxUsagePerUser: 5,
    isActive: true,
    usedCount: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Check if promotions were created:
db.promotions.find({});
*/

console.log("Sample promotions data:");
console.log(JSON.stringify(samplePromotions, null, 2));
