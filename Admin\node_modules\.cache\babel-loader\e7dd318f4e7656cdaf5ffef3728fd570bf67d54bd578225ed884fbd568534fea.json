{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nconst isReactNative = typeof global !== 'undefined' &&\n// @ts-ignore\nglobal.navigator &&\n// @ts-ignore\nglobal.navigator.product === 'ReactNative';\nconst isDOM = typeof document !== 'undefined';\n\n/**\n * Is `useLayoutEffect` in a DOM or React Native environment, otherwise resolves to useEffect\n * Only useful to avoid the console warning.\n *\n * PREFER `useEffect` UNLESS YOU KNOW WHAT YOU ARE DOING.\n *\n * @category effects\n */\nexport default isDOM || isReactNative ? useLayoutEffect : useEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "isReactNative", "global", "navigator", "product", "isDOM", "document"], "sources": ["E:/Uroom/Admin/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useIsomorphicEffect.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\nconst isReactNative = typeof global !== 'undefined' &&\n// @ts-ignore\nglobal.navigator &&\n// @ts-ignore\nglobal.navigator.product === 'ReactNative';\nconst isDOM = typeof document !== 'undefined';\n\n/**\n * Is `useLayoutEffect` in a DOM or React Native environment, otherwise resolves to useEffect\n * Only useful to avoid the console warning.\n *\n * PREFER `useEffect` UNLESS YOU KNOW WHAT YOU ARE DOING.\n *\n * @category effects\n */\nexport default isDOM || isReactNative ? useLayoutEffect : useEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAClD,MAAMC,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW;AACnD;AACAA,MAAM,CAACC,SAAS;AAChB;AACAD,MAAM,CAACC,SAAS,CAACC,OAAO,KAAK,aAAa;AAC1C,MAAMC,KAAK,GAAG,OAAOC,QAAQ,KAAK,WAAW;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeD,KAAK,IAAIJ,aAAa,GAAGD,eAAe,GAAGD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}