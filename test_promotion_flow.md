# Test Promotion Flow

## Bước 1: Tạo Sample Promotions

### Gọi API tạo sample data (cần admin token):
```bash
POST http://localhost:5000/api/promotions/create-samples
Headers: 
  Authorization: Bearer <admin_token>
  Content-Type: application/json
```

## Bước 2: Kiểm tra Promotions trong Database

### Kiểm tra trong MongoDB:
```javascript
// Connect to MongoDB
use your_database_name;

// Check all promotions
db.promotions.find({});

// Check specific promotions
db.promotions.find({ type: "PUBLIC" });
db.promotions.find({ type: "PRIVATE" });
```

## Bước 3: Test API Get User Promotions

### Gọi API lấy promotions (cần customer token):
```bash
GET http://localhost:5000/api/promotions/user
Headers: 
  Authorization: Bearer <customer_token>
```

### Expected Response:
```json
{
  "success": true,
  "promotions": [
    {
      "_id": "...",
      "code": "WELCOME20",
      "name": "Welcome 20% Off",
      "description": "20% discount for new customers",
      "discountType": "PERCENTAGE",
      "discountValue": 20,
      "maxDiscountAmount": 50,
      "minOrderAmount": 100,
      "type": "PUBLIC",
      "maxUsagePerUser": 3,
      "userUsedCount": 0,
      "canUse": true,
      "remainingUses": 3
    },
    {
      "_id": "...",
      "code": "SAVE50",
      "name": "Save $50",
      "description": "Fixed $50 discount on orders over $200",
      "discountType": "FIXED_AMOUNT",
      "discountValue": 50,
      "minOrderAmount": 200,
      "type": "PUBLIC",
      "maxUsagePerUser": 2,
      "userUsedCount": 0,
      "canUse": true,
      "remainingUses": 2
    }
  ]
}
```

## Bước 4: Test Private Promotion

### Gọi API check private promotion:
```bash
POST http://localhost:5000/api/promotions/check-private
Headers: 
  Authorization: Bearer <customer_token>
  Content-Type: application/json
Body:
{
  "code": "VIP30"
}
```

### Expected Response:
```json
{
  "success": true,
  "message": "Private promotion assigned and available",
  "promotion": {
    "_id": "...",
    "code": "VIP30",
    "name": "VIP 30% Off",
    "description": "Exclusive 30% discount for VIP members",
    "type": "PRIVATE",
    "userUsedCount": 0,
    "canUse": true
  }
}
```

## Bước 5: Test Apply Promotion

### Gọi API apply promotion:
```bash
POST http://localhost:5000/api/promotions/apply
Headers: 
  Authorization: Bearer <customer_token>
  Content-Type: application/json
Body:
{
  "code": "WELCOME20",
  "orderAmount": 150
}
```

### Expected Response:
```json
{
  "valid": true,
  "discount": 30,
  "message": "Promotion applied successfully",
  "promotionId": "..."
}
```

## Debug Steps

### 1. Check Backend Logs
- Xem console logs từ getUserPromotions
- Kiểm tra số lượng promotions ở mỗi bước

### 2. Check Frontend Console
- Xem API response trong browser console
- Kiểm tra token có được gửi đúng không

### 3. Check Database
- Verify promotions exist
- Check date ranges
- Verify user authentication

### 4. Common Issues
- **Empty promotions**: Không có promotions trong DB hoặc date range không match
- **Authentication error**: Token không hợp lệ hoặc user chưa login
- **Date filter**: startDate/endDate không trong range hiện tại
- **Usage limit**: User đã dùng hết lượt cho promotion đó

## Quick Fix Commands

### Reset sample data:
```bash
# Delete all promotions
db.promotions.deleteMany({});

# Delete all promotion users
db.promotionusers.deleteMany({});

# Then call create-samples API again
```
