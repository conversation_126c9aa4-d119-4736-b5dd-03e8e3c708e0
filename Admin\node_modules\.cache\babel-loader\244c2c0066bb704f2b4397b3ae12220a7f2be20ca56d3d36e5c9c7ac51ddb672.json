{"ast": null, "code": "'use strict';\n\n/** @type {import('./floor')} */\nmodule.exports = Math.floor;", "map": {"version": 3, "names": ["module", "exports", "Math", "floor"], "sources": ["E:/Uroom/Admin/node_modules/math-intrinsics/floor.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./floor')} */\nmodule.exports = Math.floor;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,IAAI,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}