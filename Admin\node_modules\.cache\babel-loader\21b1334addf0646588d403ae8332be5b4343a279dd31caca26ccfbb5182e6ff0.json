{"ast": null, "code": "import ReactDOM from 'react-dom';\nexport default function safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    return ReactDOM.findDOMNode(componentOrElement);\n  }\n  return componentOrElement != null ? componentOrElement : null;\n}", "map": {"version": 3, "names": ["ReactDOM", "safeFindDOMNode", "componentOrElement", "findDOMNode"], "sources": ["E:/Uroom/Admin/node_modules/react-bootstrap/esm/safeFindDOMNode.js"], "sourcesContent": ["import ReactDOM from 'react-dom';\nexport default function safeFindDOMNode(componentOrElement) {\n  if (componentOrElement && 'setState' in componentOrElement) {\n    return ReactDOM.findDOMNode(componentOrElement);\n  }\n  return componentOrElement != null ? componentOrElement : null;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,eAAe,SAASC,eAAeA,CAACC,kBAAkB,EAAE;EAC1D,IAAIA,kBAAkB,IAAI,UAAU,IAAIA,kBAAkB,EAAE;IAC1D,OAAOF,QAAQ,CAACG,WAAW,CAACD,kBAAkB,CAAC;EACjD;EACA,OAAOA,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,GAAG,IAAI;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}