{"ast": null, "code": "const _excluded = [\"enabled\", \"placement\", \"strategy\", \"modifiers\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { dequal } from 'dequal';\nimport useSafeState from '@restart/hooks/useSafeState';\nimport { createPopper } from './popper';\nconst disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false,\n  phase: 'afterWrite',\n  fn: () => undefined\n};\n\n// until docjs supports type exports...\n\nconst ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: ({\n    state\n  }) => () => {\n    const {\n      reference,\n      popper\n    } = state.elements;\n    if ('removeAttribute' in reference) {\n      const ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(id => id.trim() !== popper.id);\n      if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n    }\n  },\n  fn: ({\n    state\n  }) => {\n    var _popper$getAttribute;\n    const {\n      popper,\n      reference\n    } = state.elements;\n    const role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      const ids = reference.getAttribute('aria-describedby');\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n      reference.setAttribute('aria-describedby', ids ? `${ids},${popper.id}` : popper.id);\n    }\n  }\n};\nconst EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\nfunction usePopper(referenceElement, popperElement, _ref = {}) {\n  let {\n      enabled = true,\n      placement = 'bottom',\n      strategy = 'absolute',\n      modifiers = EMPTY_MODIFIERS\n    } = _ref,\n    config = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const prevModifiers = useRef(modifiers);\n  const popperInstanceRef = useRef();\n  const update = useCallback(() => {\n    var _popperInstanceRef$cu;\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  const forceUpdate = useCallback(() => {\n    var _popperInstanceRef$cu2;\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n  const [popperState, setState] = useSafeState(useState({\n    placement,\n    update,\n    forceUpdate,\n    attributes: {},\n    styles: {\n      popper: {},\n      arrow: {}\n    }\n  }));\n  const updateModifier = useMemo(() => ({\n    name: 'updateStateModifier',\n    enabled: true,\n    phase: 'write',\n    requires: ['computeStyles'],\n    fn: ({\n      state\n    }) => {\n      const styles = {};\n      const attributes = {};\n      Object.keys(state.elements).forEach(element => {\n        styles[element] = state.styles[element];\n        attributes[element] = state.attributes[element];\n      });\n      setState({\n        state,\n        styles,\n        attributes,\n        update,\n        forceUpdate,\n        placement: state.placement\n      });\n    }\n  }), [update, forceUpdate, setState]);\n  const nextModifiers = useMemo(() => {\n    if (!dequal(prevModifiers.current, modifiers)) {\n      prevModifiers.current = modifiers;\n    }\n    return prevModifiers.current;\n  }, [modifiers]);\n  useEffect(() => {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, updateModifier, disabledApplyStylesModifier]\n    });\n  }, [strategy, placement, updateModifier, enabled, nextModifiers]);\n  useEffect(() => {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n    popperInstanceRef.current = createPopper(referenceElement, popperElement, Object.assign({}, config, {\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, ariaDescribedByModifier, updateModifier]\n    }));\n    return () => {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(s => Object.assign({}, s, {\n          attributes: {},\n          styles: {\n            popper: {}\n          }\n        }));\n      }\n    };\n    // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\nexport default usePopper;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "useCallback", "useEffect", "useMemo", "useRef", "useState", "dequal", "useSafeState", "createPopper", "disabledApplyStylesModifier", "name", "enabled", "phase", "fn", "undefined", "ariaDescribedByModifier", "effect", "state", "reference", "popper", "elements", "ids", "getAttribute", "split", "filter", "id", "trim", "length", "removeAttribute", "setAttribute", "join", "_popper$getAttribute", "role", "toLowerCase", "EMPTY_MODIFIERS", "usePopper", "referenceElement", "popper<PERSON>lement", "_ref", "placement", "strategy", "modifiers", "config", "prevModifiers", "popperInstanceRef", "update", "_popperInstanceRef$cu", "current", "forceUpdate", "_popperInstanceRef$cu2", "popperState", "setState", "attributes", "styles", "arrow", "updateModifier", "requires", "Object", "keys", "for<PERSON>ach", "element", "nextModifiers", "setOptions", "assign", "destroy", "s"], "sources": ["E:/Uroom/Admin/node_modules/@restart/ui/esm/usePopper.js"], "sourcesContent": ["const _excluded = [\"enabled\", \"placement\", \"strategy\", \"modifiers\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { dequal } from 'dequal';\nimport useSafeState from '@restart/hooks/useSafeState';\nimport { createPopper } from './popper';\nconst disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false,\n  phase: 'afterWrite',\n  fn: () => undefined\n};\n\n// until docjs supports type exports...\n\nconst ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: ({\n    state\n  }) => () => {\n    const {\n      reference,\n      popper\n    } = state.elements;\n    if ('removeAttribute' in reference) {\n      const ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(id => id.trim() !== popper.id);\n      if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n    }\n  },\n  fn: ({\n    state\n  }) => {\n    var _popper$getAttribute;\n    const {\n      popper,\n      reference\n    } = state.elements;\n    const role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      const ids = reference.getAttribute('aria-describedby');\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n      reference.setAttribute('aria-describedby', ids ? `${ids},${popper.id}` : popper.id);\n    }\n  }\n};\nconst EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\nfunction usePopper(referenceElement, popperElement, _ref = {}) {\n  let {\n      enabled = true,\n      placement = 'bottom',\n      strategy = 'absolute',\n      modifiers = EMPTY_MODIFIERS\n    } = _ref,\n    config = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const prevModifiers = useRef(modifiers);\n  const popperInstanceRef = useRef();\n  const update = useCallback(() => {\n    var _popperInstanceRef$cu;\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  const forceUpdate = useCallback(() => {\n    var _popperInstanceRef$cu2;\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n  const [popperState, setState] = useSafeState(useState({\n    placement,\n    update,\n    forceUpdate,\n    attributes: {},\n    styles: {\n      popper: {},\n      arrow: {}\n    }\n  }));\n  const updateModifier = useMemo(() => ({\n    name: 'updateStateModifier',\n    enabled: true,\n    phase: 'write',\n    requires: ['computeStyles'],\n    fn: ({\n      state\n    }) => {\n      const styles = {};\n      const attributes = {};\n      Object.keys(state.elements).forEach(element => {\n        styles[element] = state.styles[element];\n        attributes[element] = state.attributes[element];\n      });\n      setState({\n        state,\n        styles,\n        attributes,\n        update,\n        forceUpdate,\n        placement: state.placement\n      });\n    }\n  }), [update, forceUpdate, setState]);\n  const nextModifiers = useMemo(() => {\n    if (!dequal(prevModifiers.current, modifiers)) {\n      prevModifiers.current = modifiers;\n    }\n    return prevModifiers.current;\n  }, [modifiers]);\n  useEffect(() => {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, updateModifier, disabledApplyStylesModifier]\n    });\n  }, [strategy, placement, updateModifier, enabled, nextModifiers]);\n  useEffect(() => {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n    popperInstanceRef.current = createPopper(referenceElement, popperElement, Object.assign({}, config, {\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, ariaDescribedByModifier, updateModifier]\n    }));\n    return () => {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(s => Object.assign({}, s, {\n          attributes: {},\n          styles: {\n            popper: {}\n          }\n        }));\n      }\n    };\n    // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\nexport default usePopper;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC;AACnE,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,SAASK,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACzE,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,YAAY,MAAM,6BAA6B;AACtD,SAASC,YAAY,QAAQ,UAAU;AACvC,MAAMC,2BAA2B,GAAG;EAClCC,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,YAAY;EACnBC,EAAE,EAAEA,CAAA,KAAMC;AACZ,CAAC;;AAED;;AAEA,MAAMC,uBAAuB,GAAG;EAC9BL,IAAI,EAAE,iBAAiB;EACvBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,YAAY;EACnBI,MAAM,EAAEA,CAAC;IACPC;EACF,CAAC,KAAK,MAAM;IACV,MAAM;MACJC,SAAS;MACTC;IACF,CAAC,GAAGF,KAAK,CAACG,QAAQ;IAClB,IAAI,iBAAiB,IAAIF,SAAS,EAAE;MAClC,MAAMG,GAAG,GAAG,CAACH,SAAS,CAACI,YAAY,CAAC,kBAAkB,CAAC,IAAI,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,IAAI,CAAC,CAAC,KAAKP,MAAM,CAACM,EAAE,CAAC;MAC/G,IAAI,CAACJ,GAAG,CAACM,MAAM,EAAET,SAAS,CAACU,eAAe,CAAC,kBAAkB,CAAC,CAAC,KAAKV,SAAS,CAACW,YAAY,CAAC,kBAAkB,EAAER,GAAG,CAACS,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/H;EACF,CAAC;EACDjB,EAAE,EAAEA,CAAC;IACHI;EACF,CAAC,KAAK;IACJ,IAAIc,oBAAoB;IACxB,MAAM;MACJZ,MAAM;MACND;IACF,CAAC,GAAGD,KAAK,CAACG,QAAQ;IAClB,MAAMY,IAAI,GAAG,CAACD,oBAAoB,GAAGZ,MAAM,CAACG,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGS,oBAAoB,CAACE,WAAW,CAAC,CAAC;IACvH,IAAId,MAAM,CAACM,EAAE,IAAIO,IAAI,KAAK,SAAS,IAAI,cAAc,IAAId,SAAS,EAAE;MAClE,MAAMG,GAAG,GAAGH,SAAS,CAACI,YAAY,CAAC,kBAAkB,CAAC;MACtD,IAAID,GAAG,IAAIA,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC,CAACvB,OAAO,CAACmB,MAAM,CAACM,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QACnD;MACF;MACAP,SAAS,CAACW,YAAY,CAAC,kBAAkB,EAAER,GAAG,GAAG,GAAGA,GAAG,IAAIF,MAAM,CAACM,EAAE,EAAE,GAAGN,MAAM,CAACM,EAAE,CAAC;IACrF;EACF;AACF,CAAC;AACD,MAAMS,eAAe,GAAG,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;EAC7D,IAAI;MACA3B,OAAO,GAAG,IAAI;MACd4B,SAAS,GAAG,QAAQ;MACpBC,QAAQ,GAAG,UAAU;MACrBC,SAAS,GAAGP;IACd,CAAC,GAAGI,IAAI;IACRI,MAAM,GAAGjD,6BAA6B,CAAC6C,IAAI,EAAE9C,SAAS,CAAC;EACzD,MAAMmD,aAAa,GAAGvC,MAAM,CAACqC,SAAS,CAAC;EACvC,MAAMG,iBAAiB,GAAGxC,MAAM,CAAC,CAAC;EAClC,MAAMyC,MAAM,GAAG5C,WAAW,CAAC,MAAM;IAC/B,IAAI6C,qBAAqB;IACzB,CAACA,qBAAqB,GAAGF,iBAAiB,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACD,MAAM,CAAC,CAAC;EACvG,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,WAAW,GAAG/C,WAAW,CAAC,MAAM;IACpC,IAAIgD,sBAAsB;IAC1B,CAACA,sBAAsB,GAAGL,iBAAiB,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,sBAAsB,CAACD,WAAW,CAAC,CAAC;EAC9G,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACE,WAAW,EAAEC,QAAQ,CAAC,GAAG5C,YAAY,CAACF,QAAQ,CAAC;IACpDkC,SAAS;IACTM,MAAM;IACNG,WAAW;IACXI,UAAU,EAAE,CAAC,CAAC;IACdC,MAAM,EAAE;MACNlC,MAAM,EAAE,CAAC,CAAC;MACVmC,KAAK,EAAE,CAAC;IACV;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,cAAc,GAAGpD,OAAO,CAAC,OAAO;IACpCO,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,OAAO;IACd4C,QAAQ,EAAE,CAAC,eAAe,CAAC;IAC3B3C,EAAE,EAAEA,CAAC;MACHI;IACF,CAAC,KAAK;MACJ,MAAMoC,MAAM,GAAG,CAAC,CAAC;MACjB,MAAMD,UAAU,GAAG,CAAC,CAAC;MACrBK,MAAM,CAACC,IAAI,CAACzC,KAAK,CAACG,QAAQ,CAAC,CAACuC,OAAO,CAACC,OAAO,IAAI;QAC7CP,MAAM,CAACO,OAAO,CAAC,GAAG3C,KAAK,CAACoC,MAAM,CAACO,OAAO,CAAC;QACvCR,UAAU,CAACQ,OAAO,CAAC,GAAG3C,KAAK,CAACmC,UAAU,CAACQ,OAAO,CAAC;MACjD,CAAC,CAAC;MACFT,QAAQ,CAAC;QACPlC,KAAK;QACLoC,MAAM;QACND,UAAU;QACVP,MAAM;QACNG,WAAW;QACXT,SAAS,EAAEtB,KAAK,CAACsB;MACnB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,EAAE,CAACM,MAAM,EAAEG,WAAW,EAAEG,QAAQ,CAAC,CAAC;EACpC,MAAMU,aAAa,GAAG1D,OAAO,CAAC,MAAM;IAClC,IAAI,CAACG,MAAM,CAACqC,aAAa,CAACI,OAAO,EAAEN,SAAS,CAAC,EAAE;MAC7CE,aAAa,CAACI,OAAO,GAAGN,SAAS;IACnC;IACA,OAAOE,aAAa,CAACI,OAAO;EAC9B,CAAC,EAAE,CAACN,SAAS,CAAC,CAAC;EACfvC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0C,iBAAiB,CAACG,OAAO,IAAI,CAACpC,OAAO,EAAE;IAC5CiC,iBAAiB,CAACG,OAAO,CAACe,UAAU,CAAC;MACnCvB,SAAS;MACTC,QAAQ;MACRC,SAAS,EAAE,CAAC,GAAGoB,aAAa,EAAEN,cAAc,EAAE9C,2BAA2B;IAC3E,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC+B,QAAQ,EAAED,SAAS,EAAEgB,cAAc,EAAE5C,OAAO,EAAEkD,aAAa,CAAC,CAAC;EACjE3D,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,OAAO,IAAIyB,gBAAgB,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;MACjE,OAAOvB,SAAS;IAClB;IACA8B,iBAAiB,CAACG,OAAO,GAAGvC,YAAY,CAAC4B,gBAAgB,EAAEC,aAAa,EAAEoB,MAAM,CAACM,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,EAAE;MAClGH,SAAS;MACTC,QAAQ;MACRC,SAAS,EAAE,CAAC,GAAGoB,aAAa,EAAE9C,uBAAuB,EAAEwC,cAAc;IACvE,CAAC,CAAC,CAAC;IACH,OAAO,MAAM;MACX,IAAIX,iBAAiB,CAACG,OAAO,IAAI,IAAI,EAAE;QACrCH,iBAAiB,CAACG,OAAO,CAACiB,OAAO,CAAC,CAAC;QACnCpB,iBAAiB,CAACG,OAAO,GAAGjC,SAAS;QACrCqC,QAAQ,CAACc,CAAC,IAAIR,MAAM,CAACM,MAAM,CAAC,CAAC,CAAC,EAAEE,CAAC,EAAE;UACjCb,UAAU,EAAE,CAAC,CAAC;UACdC,MAAM,EAAE;YACNlC,MAAM,EAAE,CAAC;UACX;QACF,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IACD;IACA;EACF,CAAC,EAAE,CAACR,OAAO,EAAEyB,gBAAgB,EAAEC,aAAa,CAAC,CAAC;EAC9C,OAAOa,WAAW;AACpB;AACA,eAAef,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}